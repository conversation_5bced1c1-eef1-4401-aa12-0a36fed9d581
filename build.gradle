group 'io.flutter.plugins.sharedpreferences'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.4.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

allprojects {
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
        }
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
        disable 'GradleDependency'
        baseline file("lint-baseline.xml")
    }
    dependencies {
        testImplementation 'junit:junit:4.12'
        testImplementation 'org.mockito:mockito-inline:3.9.0'
    }


    testOptions {
        unitTests.includeAndroidResources = true
        unitTests.returnDefaultValues = true
        unitTests.all {
            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
