package com.baidu.mapapi.search;

import java.util.LinkedHashMap;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodChannel;

public class MethodChannelManager {
    private static MethodChannelManager sInstance;

//    private MethodChannel mSearchChannel;

    public static MethodChannelManager getInstance() {
        if (null == sInstance) {
            sInstance = new MethodChannelManager();
        }

        return sInstance;
    }

    private LinkedHashMap<Integer, MethodChannel> mSearchChannelStack = new LinkedHashMap<>();

    private int currentFlutterEngineHashCode;

    public void setCurrentEngine(FlutterEngine engine) {
        if (engine != null) {
            currentFlutterEngineHashCode = engine.getDartExecutor().getBinaryMessenger().hashCode();
        }
    }


    public int getCurrentFlutterEngineHashCode(){
        return currentFlutterEngineHashCode;
    }

    public void putSearchChannel(BinaryMessenger binaryMessenger, MethodChannel methodChannel) {
        mSearchChannelStack.put(binaryMessenger.hashCode(), methodChannel);
    }

    public MethodChannel getSearchChannel() {
        MethodChannel methodChannel = mSearchChannelStack.get(currentFlutterEngineHashCode);
        if (methodChannel != null) {
            return methodChannel;
        } else {
            if (!mSearchChannelStack.isEmpty()) {
                return (MethodChannel) mSearchChannelStack.entrySet().toArray()[mSearchChannelStack.size() - 1];
            }
        }
        return null;
    }

}
