<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_warning"
        android:layout_width="0dp"
        android:layout_height="30dp"
        android:background="@color/color_FFFEF2"
        android:gravity="center"
        android:text="只可对私海客户中已注册的客户进行推荐，每次最多10个客户"
        android:textColor="@color/color_F96A0E"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.xyy.common.widget.statusview.StatusViewLayout
        android:id="@+id/svl"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="75dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_warning">

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/srl"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    </com.xyy.common.widget.statusview.StatusViewLayout>

    <include
        layout="@layout/bottom_cart"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_margin="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>