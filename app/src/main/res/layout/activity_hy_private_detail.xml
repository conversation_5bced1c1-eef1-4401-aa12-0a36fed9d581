<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginEnd="@dimen/dp10"
        app:cardCornerRadius="2dp"
        app:cardElevation="2dp"
        app:cardUseCompatPadding="false">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rl_mine_drugstore_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingLeft="@dimen/dp10"
            android:paddingRight="@dimen/dp10">

            <TextView
                android:id="@+id/tv_mine_drugstore_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_marginTop="@dimen/dp10"
                android:textColor="@color/text_color_333333"
                android:textSize="@dimen/dp16"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="监利县四季康药店天府路店" />

            <TextView
                android:id="@+id/tv_mine_drugstore_serial_hint"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:gravity="center_horizontal"
                android:includeFontPadding="false"
                android:text="@string/mine_drugstore_serial"
                android:textColor="@color/color_8E8E93"
                android:textSize="@dimen/sp14"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_mine_drugstore_name" />

            <TextView
                android:id="@+id/tv_mine_drugstore_serial_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:includeFontPadding="false"
                android:textColor="@color/color_8E8E93"
                android:textSize="@dimen/sp14"
                app:layout_constraintLeft_toRightOf="@id/tv_mine_drugstore_serial_hint"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_mine_drugstore_name"
                tools:text="131" />

            <TextView
                android:id="@+id/tv_mine_drugstore_poi_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:includeFontPadding="false"
                android:textColor="@color/color_8E8E93"
                android:textSize="@dimen/sp14"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_mine_drugstore_serial_number"
                tools:text="POI ID：11212" />
            <TextView
                android:id="@+id/tv_mine_drugstore_hy_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:includeFontPadding="false"
                android:textColor="@color/color_8E8E93"
                android:textSize="@dimen/sp14"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_mine_drugstore_poi_id"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="荷叶门店ID：11212" />

            <TextView
                android:id="@+id/tv_mine_drugstore_register_address"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:includeFontPadding="false"
                android:textColor="@color/color_8E8E93"
                android:textSize="@dimen/sp14"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_mine_drugstore_hy_id"
                tools:text="注册地址：湖北省武汉市江夏区金融港" />

            <View
                android:id="@+id/line1"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="10dp"
                android:background="@color/line_color"
                app:layout_constraintTop_toBottomOf="@id/tv_mine_drugstore_register_address" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/tv_group_register"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="tv_mine_drugstore_register_address
            ,tv_mine_drugstore_serial_number
            ,tv_mine_drugstore_serial_hint"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_mine_drugstore_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:text="门店地址："
                android:textColor="@color/color_00B377"
                android:textSize="14sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/line1" />

            <TextView
                android:id="@+id/tv_mine_drugstore_address"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="20dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/color_00B377"
                android:textSize="@dimen/sp14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/tv_mine_drugstore_location"
                app:layout_constraintRight_toLeftOf="@+id/iv_right"
                app:layout_constraintTop_toBottomOf="@id/line1"
                tools:text="江夏区光谷大道11号" />

            <ImageView
                android:id="@+id/iv_right"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginRight="10dp"
                android:src="@drawable/icon_right_arrow"
                android:tint="#DBDBDB"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/iv_call"
                app:layout_constraintTop_toBottomOf="@id/line1" />

            <ImageView
                android:id="@+id/iv_call"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:src="@drawable/ydxx_call"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line1" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <com.xyy.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/cv_container"
        android:layout_margin="10dp"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="7dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_grid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.xyy.common.widget.RoundLinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/operate_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp60"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="0dp"
        android:background="@color/white"
        android:orientation="horizontal">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_line"
            app:layout_constraintTop_toTopOf="parent" />

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/bt_add_schedule"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp40"
            android:layout_marginStart="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/button_background"
            android:gravity="center_horizontal"
            android:includeFontPadding="false"
            android:padding="@dimen/dp10"
            android:text="@string/add_visit"
            android:textColor="@color/white"
            android:textSize="@dimen/sp16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_backgroundColor="@color/text_color_35C561"
            app:rv_cornerRadius="5dp"
            app:rv_isRadiusHalfHeight="false"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>