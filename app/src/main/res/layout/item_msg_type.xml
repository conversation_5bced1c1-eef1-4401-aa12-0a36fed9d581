<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="78dp"
    android:background="#ffffff"
    android:padding="15dp">


    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_dot"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:background="@drawable/ic_circle"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="@+id/iv_icon"
        app:layout_constraintTop_toTopOf="@+id/iv_icon" />


    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/color_292933"
        android:textSize="17sp"
        app:layout_constraintLeft_toRightOf="@id/iv_icon"
        app:layout_constraintRight_toLeftOf="@+id/tv_date"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="订单卡单提醒" />

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_9494A6"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        tools:text="2020-07-07" />


    <TextView
        android:id="@+id/tv_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/color_9494A6"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="武汉市老百姓大药房因资质过期导致订单发武汉市老百姓大药房因资质过期导致订单发" />


</androidx.constraintlayout.widget.ConstraintLayout>