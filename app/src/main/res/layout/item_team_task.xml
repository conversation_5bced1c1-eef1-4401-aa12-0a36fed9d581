<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_292933"
        android:textSize="15sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@+id/progress_bar"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="杜萨的卡拉斯" />

    <ProgressBar
        android:id="@+id/progress_bar"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="86dp"
        android:layout_height="8dp"
        android:max="100"
        android:progressDrawable="@drawable/progress_common_horizontal_drawable"
        app:layout_constraintBottom_toBottomOf="@+id/tv_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_name" />

    <TextView
        android:id="@+id/tv_sales_degree_of_completion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="10dp"
        android:textSize="12sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@+id/tv_progress_bar_value"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_name"
        tools:text="361278/139812" />

    <TextView
        android:id="@+id/tv_progress_bar_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_676773"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_sales_degree_of_completion"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_sales_degree_of_completion"
        tools:text="达成50%" />

</androidx.constraintlayout.widget.ConstraintLayout>