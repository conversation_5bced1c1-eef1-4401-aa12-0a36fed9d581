<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.statusview.StatusViewLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/svl"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/ic_shadow"
                app:layout_scrollFlags="scroll|enterAlwaysCollapsed">

                <TextView
                    android:id="@+id/tv_task_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="10dp"
                    android:textColor="@color/color_292933"
                    android:textSize="15sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="任务名称：这里是任务名称任务名称名称显示不下 就折行：这里是任务名称任务名称名称显示不下 " />

                <TextView
                    android:id="@+id/tv_variety"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:textColor="@color/color_676773"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toEndOf="@+id/tv_task_name"
                    app:layout_constraintStart_toStartOf="@+id/tv_task_name"
                    app:layout_constraintTop_toBottomOf="@+id/tv_task_name"
                    tools:text="品种数量：1" />

                <TextView
                    android:id="@+id/tv_sales_degree_of_completion"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:textColor="@color/color_676773"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toEndOf="@+id/tv_task_name"
                    app:layout_constraintStart_toStartOf="@+id/tv_task_name"
                    app:layout_constraintTop_toBottomOf="@+id/tv_variety"
                    tools:text="销售额进展度：500元/1000元" />

                <ProgressBar
                    android:id="@+id/progress_bar"
                    style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                    android:layout_width="0dp"
                    android:layout_height="8dp"
                    android:layout_marginTop="7dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="15dp"
                    android:max="100"
                    android:progressDrawable="@drawable/progress_common_horizontal_drawable"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/tv_progress_bar_value"
                    app:layout_constraintStart_toStartOf="@+id/tv_task_name"
                    app:layout_constraintTop_toBottomOf="@+id/tv_sales_degree_of_completion"
                    app:layout_constraintVertical_bias="0" />

                <TextView
                    android:id="@+id/tv_progress_bar_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_676773"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="@+id/progress_bar"
                    app:layout_constraintEnd_toEndOf="@+id/tv_task_name"
                    app:layout_constraintTop_toTopOf="@+id/progress_bar"
                    tools:text="达成50%" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp">

                <TextView
                    android:id="@+id/tv_sort_task_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:drawableRight="@drawable/ic_sort_desc"
                    android:drawablePadding="5dp"
                    android:paddingBottom="15dp"
                    android:text="任务进展"
                    android:textColor="@color/color_292933"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_sort_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="43dp"
                    android:drawableRight="@drawable/ic_sort_normal"
                    android:drawablePadding="5dp"
                    android:paddingBottom="15dp"
                    android:text="价格"
                    android:textColor="@color/color_676773"
                    android:textSize="14sp"
                    app:layout_constraintStart_toEndOf="@+id/tv_sort_task_progress"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:background="@color/text_color_F6F6F6"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_sort_task_progress" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/srl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="75dp"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="75dp"
            android:layout_gravity="bottom"
            android:background="@color/white"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingStart="15dp"
            android:paddingEnd="15dp">

            <com.xyy.common.widget.RoundTextView
                android:id="@+id/rtv_recommend"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:gravity="center"
                android:text="推荐"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:rv_backgroundColor="@color/color_00B377"
                app:rv_cornerRadius="2dp" />
        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</com.xyy.common.widget.statusview.StatusViewLayout>