<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_gray_EFEFF4"
    android:orientation="vertical">

    <View style="@style/schedule_divide" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp44"
        android:background="@color/white">

        <TextView
            android:id="@+id/tv_merchantName_key"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp10"
            android:text="客户名称"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/sp16" />

        <TextView
            android:id="@+id/tv_merchantName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp10"
            android:ellipsize="end"
            android:maxEms="10"
            android:maxLines="1"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/sp16"
            tools:text="湖北大药房湖北大药房药房药房药房湖北大药房药房药房药房药房药房湖北大药药房湖北大药房药房药药房湖北大药房药房药药房湖北大药房药房药" />

    </RelativeLayout>

    <View style="@style/schedule_line" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/schedule_item_height"
        android:background="@color/white">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp10"
            android:text="原发票类型"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/sp16" />

        <TextView
            android:id="@+id/tv_invoiceOriginalType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp10"
            android:ellipsize="end"
            android:lines="1"
            android:maxWidth="@dimen/dp250"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/sp16"
            tools:text="电子普通发票" />

    </RelativeLayout>

    <View style="@style/schedule_line" />

    <com.ybm100.app.crm.widget.SimpleItemView
        android:id="@+id/siv_invoiceType"
        android:layout_width="match_parent"
        android:layout_height="@dimen/schedule_item_height"
        android:background="@color/white"
        app:left_text_content="申请发票类型"
        app:right_edit_editable="false"
        app:right_edit_hint="请选择发票类型" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp10"
        android:layout_marginTop="6dp"
        android:layout_marginRight="@dimen/dp10"
        android:text="【专票开票信息需在添加首页资质/资质变更中拍照上传】"
        android:textColor="@color/text_color_333333"
        android:textSize="14sp" />
</LinearLayout>
