<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.xyy.common.widget.statusview.StatusViewLayout
        android:id="@+id/status_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/ll_bottom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/color_gray_EFEFF4"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    android:paddingStart="@dimen/dp15"
                    android:paddingTop="@dimen/dp15"
                    android:paddingEnd="@dimen/dp15"
                    android:paddingBottom="5dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:text="发票类型："
                        android:textColor="@color/text_color_333333"
                        android:textSize="@dimen/sp15"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_invoice_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_color_333333"
                        android:textSize="@dimen/sp15"
                        android:textStyle="bold" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_invoice_explain"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:paddingStart="@dimen/dp15"
                    android:paddingEnd="@dimen/dp15"
                    android:paddingBottom="@dimen/dp15"
                    android:textColor="#FF8E8E93"
                    android:textSize="@dimen/sp12"
                    tools:text="湖北分公司7月份纸质普通发票申请设置上限200家客户，目前已
通过申请180家，申请超过200家后，需由PM进行审核" />

                <TextView
                    android:id="@+id/tv_tips"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/dp15"
                    android:paddingTop="@dimen/dp15"
                    android:paddingEnd="@dimen/dp15"
                    android:paddingBottom="5dp"
                    android:text="发票申请记录"
                    android:textColor="#FF8E8E93"
                    android:textSize="@dimen/sp12" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_invoice"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </LinearLayout>

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    </com.xyy.common.widget.statusview.StatusViewLayout>

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@color/white"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:id="@+id/tv_invoice_apply"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dp10"
            android:background="@drawable/shape_invoice_apply"
            android:gravity="center"
            android:text="发票类型申请"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>