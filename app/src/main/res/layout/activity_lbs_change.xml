<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_gray_EFEFF4"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp10"
        android:text="客户地址"
        android:textColor="@color/text_color_333333"
        android:textSize="@dimen/sample_text_size" />

    <LinearLayout
        style="@style/clue_edit_layout"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_address"
            style="@style/clue_edit_text_left"
            android:drawableLeft="@drawable/icon_location_address"
            android:drawablePadding="5dp"
            android:ellipsize="end"
            android:singleLine="true"
            tools:text="湖北省武汉市江夏区金融港三路A2金融港三路A2金融港三路A2" />

        <TextView
            android:id="@+id/tv_remark"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:paddingLeft="5dp"
            android:text="重新标记"
            android:textColor="@color/text_color_35C561" />
    </LinearLayout>

    <LinearLayout style="@style/clue_edit_layout">

        <TextView
            style="@style/clue_edit_text_left"
            android:text="详细地址" />

        <com.ybm100.app.crm.widget.EditTextWithDel
            android:id="@+id/et_detail_address"
            style="@style/clue_edit_text_right"
            android:hint="请输入详细地址"
            android:maxLength="50" />
    </LinearLayout>
</LinearLayout>