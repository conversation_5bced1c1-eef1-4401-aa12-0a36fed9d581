<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:orientation="vertical"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="@dimen/dp8">

    <LinearLayout
        android:id="@+id/layout_drugstore_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp10"
        android:paddingTop="12dp"
        android:paddingRight="@dimen/dp10">


        <TextView
            android:id="@+id/tv_drugstore_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp35"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_color_333333"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="武汉康城美汇大药房有限公司武汉康城美汇大药房有限公司武汉康城美汇大药房有限公司" />

        <TextView
            android:id="@+id/tv_drugstore_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_color_35C561"
            android:textSize="14sp"
            tools:text="已注册" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_distance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp10"
        android:layout_marginTop="5dp"
        android:textColor="@color/color_666666"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_drugstore_name"
        tools:text="距您40m" />

    <TextView
        android:id="@+id/tv_address"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:paddingStart="@dimen/dp10"
        android:paddingEnd="@dimen/dp10"
        android:singleLine="true"
        android:textColor="@color/color_666666"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/tv_distance"
        app:layout_constraintTop_toTopOf="@+id/tv_distance"
        tools:text="湖北省武汉市洪山区东湖高薪技术开发区啊啊啊湖北省武汉市洪山区东湖高薪技术开发区啊啊啊…" />

    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/flexBoxLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        app:alignContent="flex_start"
        app:flexDirection="row"
        app:flexWrap="wrap"
        app:justifyContent="flex_start"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_distance"
        app:showDividerHorizontal="middle">

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_licence_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp7"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="5dp"
            android:paddingTop="@dimen/dp2"
            android:paddingEnd="5dp"
            android:paddingBottom="@dimen/dp1"
            android:textColor="@color/color_ECA100"
            android:textSize="@dimen/sp10"
            app:rv_backgroundColor="#0bffae00"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="@color/color_ECA100"
            app:rv_strokeWidth="1px"
            tools:text="资质已提交" />

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_drugstore_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp7"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="5dp"
            android:paddingTop="@dimen/dp2"
            android:paddingEnd="5dp"
            android:paddingBottom="@dimen/dp1"
            android:textColor="@color/text_color_35C561"
            android:textSize="@dimen/sp10"
            app:rv_backgroundColor="#0d00b377"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="@color/text_color_35C561"
            app:rv_strokeWidth="1px"
            tools:text="单体药店" />

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_drugstore_level"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp7"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="5dp"
            android:paddingTop="@dimen/dp2"
            android:paddingEnd="5dp"
            android:paddingBottom="@dimen/dp1"
            android:textColor="@color/text_color_35C561"
            android:textSize="@dimen/sp10"
            app:layout_constraintLeft_toRightOf="@+id/tv_drugstore_type"
            app:rv_backgroundColor="#0d00b377"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="@color/text_color_35C561"
            app:rv_strokeWidth="1px"
            tools:text="S级别" />

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_drugstore_life"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp7"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="5dp"
            android:paddingTop="@dimen/dp2"
            android:paddingEnd="5dp"
            android:paddingBottom="@dimen/dp1"
            android:textColor="@color/text_color_35C561"
            android:textSize="@dimen/sp10"
            app:layout_constraintLeft_toRightOf="@+id/tv_drugstore_type"
            app:rv_backgroundColor="#0d00b377"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="@color/text_color_35C561"
            app:rv_strokeWidth="1px"
            tools:text="潜在" />

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_drugstore_audit_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp7"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="5dp"
            android:paddingTop="@dimen/dp2"
            android:paddingEnd="5dp"
            android:paddingBottom="@dimen/dp1"
            android:textColor="@color/text_color_35C561"
            android:textSize="@dimen/sp10"
            app:layout_constraintLeft_toRightOf="@+id/tv_drugstore_type"
            app:rv_backgroundColor="#0d00b377"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="@color/text_color_35C561"
            app:rv_strokeWidth="1px"
            tools:text="审核状态"
            tools:visibility="visible" />

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_drugstore_new_poi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp7"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingStart="5dp"
            android:paddingTop="@dimen/dp2"
            android:paddingEnd="5dp"
            android:paddingBottom="@dimen/dp1"
            android:text="新开业"
            android:textColor="#FF2121"
            android:textSize="@dimen/sp10"
            app:rv_backgroundColor="#0dFF2121"
            app:rv_cornerRadius="1dp"
            app:rv_strokeColor="#FF2121"
            app:rv_strokeWidth="1px" />

    </com.google.android.flexbox.FlexboxLayout>

    <TextView
        android:id="@+id/tv_lastTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="10dp"
        android:drawableLeft="@drawable/ic_drugstore_private_last_time"
        android:drawablePadding="4dp"
        android:ellipsize="start"
        android:singleLine="true"
        android:textSize="12sp"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintBottom_toBottomOf="@id/flexBoxLayout_serviceLines"
        app:layout_constraintLeft_toRightOf="@id/flexBoxLayout_serviceLines"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/flexBoxLayout_serviceLines"
        tools:text="距离上次拜访999天" />

    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/flexBoxLayout_serviceLines"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp10"
        android:layout_marginTop="@dimen/dp7"
        app:alignContent="flex_start"
        app:dividerDrawable="@drawable/shape_flexbox_divider"
        app:flexDirection="row"
        app:flexWrap="wrap"
        app:justifyContent="flex_start"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tv_lastTime"
        app:layout_constraintTop_toBottomOf="@+id/flexBoxLayout"
        app:showDivider="middle"
        app:showDividerHorizontal="middle" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraint_sku"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:background="@color/color_FAFAFA"
        android:padding="@dimen/dp10"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/flexBoxLayout_serviceLines"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_center_horizon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <TextView
            android:id="@+id/tv_buy_curr_key"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="本月采购额"
            android:textColor="@color/color_8E8E93"
            android:textSize="@dimen/sp12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_buy_curr_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp5"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp13"
            app:layout_constraintBottom_toBottomOf="@+id/tv_buy_curr_key"
            app:layout_constraintStart_toEndOf="@+id/tv_buy_curr_key"
            tools:text="12438.99万" />

        <TextView
            android:id="@+id/tv_buy_sku_curr_key"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="本月采购订单数"
            android:textColor="@color/color_8E8E93"
            android:textSize="@dimen/sp12"
            app:layout_constraintStart_toEndOf="@id/guideline_center_horizon"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_buy_sku_curr_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp5"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp13"
            app:layout_constraintBottom_toBottomOf="@+id/tv_buy_sku_curr_key"
            app:layout_constraintStart_toEndOf="@+id/tv_buy_sku_curr_key"
            tools:text="12438.99个" />

        <TextView
            android:id="@+id/tv_buy_last_key"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp11"
            android:text="上月采购额"
            android:textColor="@color/color_8E8E93"
            android:textSize="@dimen/sp12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_buy_curr_key" />

        <TextView
            android:id="@+id/tv_buy_last_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp5"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp13"
            app:layout_constraintBottom_toBottomOf="@+id/tv_buy_last_key"
            app:layout_constraintStart_toEndOf="@+id/tv_buy_last_key"
            tools:text="12438.99万" />

        <TextView
            android:id="@+id/tv_buy_sku_last_key"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp11"
            android:text="上月采购订单数"
            android:textColor="@color/color_8E8E93"
            android:textSize="@dimen/sp12"
            app:layout_constraintStart_toEndOf="@id/guideline_center_horizon"
            app:layout_constraintTop_toBottomOf="@+id/tv_buy_curr_key" />

        <TextView
            android:id="@+id/tv_buy_sku_last_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp5"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp13"
            app:layout_constraintBottom_toBottomOf="@+id/tv_buy_sku_last_key"
            app:layout_constraintStart_toEndOf="@+id/tv_buy_sku_last_key"
            tools:text="12438.99个" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_last"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_buy_last_key,tv_buy_last_value,tv_buy_sku_last_key,tv_buy_sku_last_value" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_seal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="@dimen/dp10"
        android:src="@drawable/seal"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/constraint_sku"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/constraint_sku"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/ll_sku_collect_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/constraint_sku" />


    <View
        android:id="@+id/v_line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/color_F6F6F6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_sku_collect_container" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_operation"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="end|center_vertical"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/v_line">

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_operation_assigned"
            style="@style/operation_drugstore_short"
            android:layout_marginEnd="@dimen/dp10"
            android:text="@string/assigned"
            android:textColor="@color/text_color_333333"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/tv_operation_phone"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_isRadiusHalfHeight="true"
            app:rv_strokeColor="@color/color_8E8E93"
            app:rv_strokeWidth="1px" />

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_operation_phone"
            style="@style/operation_drugstore"
            android:layout_marginRight="@dimen/dp10"
            android:text="@string/operation_phone"
            android:textColor="@color/text_color_333333"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/tv_operation_add_schedule"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_isRadiusHalfHeight="true"
            app:rv_strokeColor="@color/color_8E8E93"
            app:rv_strokeWidth="1px" />

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_operation_add_schedule"
            style="@style/operation_drugstore"
            android:layout_marginEnd="@dimen/dp10"
            android:text="@string/add_visit"
            android:textColor="@color/text_color_333333"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_isRadiusHalfHeight="true"
            app:rv_strokeColor="@color/color_8E8E93"
            app:rv_strokeWidth="1px" />

        <RelativeLayout
            android:id="@+id/rl_item_drugstore_shopping"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingLeft="@dimen/dp10"
            android:paddingRight="@dimen/dp15"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:src="@drawable/icon_drugstore_shopping_cart" />

            <com.ybm100.app.crm.widget.BadgeView
                android:id="@+id/tv_item_drugstore_shopping_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp13"
                android:layout_marginTop="@dimen/dp6" />

        </RelativeLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</com.xyy.common.widget.RoundConstraintLayout>