<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:paddingTop="12dp"
    android:paddingBottom="15dp"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="8dp">

    <TextView
        android:id="@+id/tv_table_name_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:textColor="@color/text_color_333333"
        android:textSize="15sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="经营状况信息变更" />

    <TextView
        android:id="@+id/tv_table_name_add_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="2dp"
        android:text="(新增)"
        android:textColor="@color/text_color_8E8E93"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/tv_table_name_add"
        app:layout_constraintLeft_toRightOf="@id/tv_table_name_add"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_info_operator_add_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:ellipsize="end"
        android:maxEms="5"
        android:singleLine="true"
        android:textColor="@color/text_color_8E8E93"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_table_name_add"
        tools:text="张珊珊张" />

    <TextView
        android:id="@+id/tv_info_operator_add_serial_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="1dp"
        android:textColor="@color/text_color_8E8E93"
        android:textSize="12sp"
        app:layout_constraintLeft_toRightOf="@id/tv_info_operator_add_name"
        app:layout_constraintTop_toBottomOf="@id/tv_table_name_add"
        tools:text="(80800123)" />

    <TextView
        android:id="@+id/tv_info_operator_add_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:textColor="@color/text_color_8E8E93"
        android:textSize="12sp"
        app:layout_constraintLeft_toRightOf="@id/tv_info_operator_add_serial_number"
        app:layout_constraintTop_toBottomOf="@id/tv_table_name_add"
        tools:text="2018.11.11 13:00:00" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="10dp"
        android:background="@color/color_line"
        app:layout_constraintTop_toBottomOf="@id/tv_info_operator_add_name" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_info_operator_add_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />
</com.xyy.common.widget.RoundConstraintLayout>
