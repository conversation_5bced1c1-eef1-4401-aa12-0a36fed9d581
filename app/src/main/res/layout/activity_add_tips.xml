<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <EditText
        android:id="@+id/et_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:singleLine="true"
        android:hint="请输入"
        android:maxLength="8"
        android:padding="16dp"
        android:textColor="@color/color_292933"
        android:textColorHint="@color/color_9494A6"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingEnd="@dimen/dp15"
        android:text="0/8"
        android:textColor="@color/color_9494A6"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/et_tips"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="15dp"
        android:background="@color/color_F5F5F5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_tips" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_history"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_history_title,scrollView_history_tags"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_history_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="29dp"
        android:paddingStart="@dimen/dp15"
        android:text="历史录入"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <ScrollView
        android:id="@+id/scrollView_history_tags"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp7"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="@dimen/dp7"
        android:layout_marginBottom="15dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/tv_add_confirm"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_history_title">

        <com.xyy.common.widget.flowtag.FlowTagLayout
            android:id="@+id/tfl_history_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </ScrollView>

    <com.xyy.common.widget.RoundTextView
        android:id="@+id/tv_add_confirm"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_margin="@dimen/dp15"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/confirm"
        android:textColor="@color/white"
        android:textSize="@dimen/sp16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rv_backgroundColor="@color/color_D8D8D8" />
</androidx.constraintlayout.widget.ConstraintLayout>


