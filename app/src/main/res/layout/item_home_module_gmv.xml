<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="89dp"
    android:orientation="vertical">


    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="17dp"
        android:gravity="left|bottom"
        android:textColor="#9494a6"
        android:textSize="12sp"
        android:layout_weight="1"
        tools:text="销售GMV(元)" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="29dp"
        android:textColor="#0D0E10"
        app:autoSizeTextType="uniform"
        android:gravity="left|center_vertical"
        android:textSize="20sp"
        app:autoSizeStepGranularity="2sp"
        android:textStyle="bold"
        tools:text="3,124,432.00" />

    <TextView
        android:id="@+id/tv_rate"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        android:layout_marginTop="1dp"
        android:gravity="left|center_vertical"
        android:textColor="#9494a6"
        android:textSize="11sp"
        tools:text="环比↑12%" />

</LinearLayout>