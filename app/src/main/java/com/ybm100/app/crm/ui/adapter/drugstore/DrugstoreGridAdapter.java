package com.ybm100.app.crm.ui.adapter.drugstore;

import androidx.annotation.Nullable;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.DrugstoreBaseBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-07-19
 */
public class DrugstoreGridAdapter extends BaseQuickAdapter<DrugstoreBaseBean, BaseViewHolder> {
    public DrugstoreGridAdapter(int layoutResId, @Nullable List<DrugstoreBaseBean> data) {
        super(layoutResId, data);
    }

    public DrugstoreGridAdapter(@Nullable List<DrugstoreBaseBean> data) {
        super(data);
    }

    public DrugstoreGridAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, DrugstoreBaseBean item) {
        helper.setText(R.id.grid_tv, item.getTitle());
        ImageView iv = helper.getView(R.id.grid_iv);
        iv.setImageResource(item.getDrawable());
    }
}
