package com.ybm100.app.crm.bean.drugstore;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 公海客户
 */
public class CustomerPublicBean implements Serializable {
    private List<RowBean> rows;
    private boolean lastPage;

    public boolean isLastPage() {
        return lastPage;
    }

    public void setLastPage(boolean lastPage) {
        this.lastPage = lastPage;
    }

    public static class RowBean implements Serializable {
        /**
         * address		string	地址
         * customerName		string	客户名称
         * distance		string	当前距离
         * id		number	@mock=$order(376,374)
         * receiveType		number	1、已被认领 2未被认领
         * sysUserId		number	@mock=$order(3049,3049)
         * sysUserName		string	@mock=$order('xt_zhongtai_24','xt_zhongtai_24')
         */
        private String address;
        private String ecAddress;
        private String customerName;
        private String ecCustomerName;
        private String distance;
        private String id;
        private String receiveType;
        private String sysUserId;
        private String sysUserName;
        private String registerFlag;
        private String merchantId;
        private String merchantStatus;
        private List<ServiceLines> serviceLines;// 合作的业务状态
        private String poiRegisterFlag;

        private List<SkuCollect> bindSkuCollect;// 绑定的商品集


        public List<SkuCollect> getBindSkuCollect() {
            return bindSkuCollect;
        }

        public void setBindSkuCollect(List<SkuCollect> bindSkuCollect) {
            this.bindSkuCollect = bindSkuCollect;
        }

        public String getPoiRegisterFlag() {
            return poiRegisterFlag;
        }

        public void setPoiRegisterFlag(String poiRegisterFlag) {
            this.poiRegisterFlag = poiRegisterFlag;
        }

        public String getMerchantId() {
            return merchantId;
        }

        public void setMerchantId(String merchantId) {
            this.merchantId = merchantId;
        }

        public String getMerchantStatus() {
            return merchantStatus;
        }

        public void setMerchantStatus(String merchantStatus) {
            this.merchantStatus = merchantStatus;
        }

        public List<ServiceLines> getServiceLines() {
            return serviceLines;
        }

        public void setServiceLines(List<ServiceLines> serviceLines) {
            this.serviceLines = serviceLines;
        }

        public String getRegisterFlag() {
            return registerFlag;
        }

        public void setRegisterFlag(String registerFlag) {
            this.registerFlag = registerFlag;
        }

        public String getEcAddress() {
            return ecAddress;
        }

        public void setEcAddress(String ecAddress) {
            this.ecAddress = ecAddress;
        }

        public String getEcCustomerName() {
            return ecCustomerName;
        }

        public void setEcCustomerName(String ecCustomerName) {
            this.ecCustomerName = ecCustomerName;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getDistance() {
            return distance;
        }

        public void setDistance(String distance) {
            this.distance = distance;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getReceiveType() {
            return receiveType;
        }

        public void setReceiveType(String receiveType) {
            this.receiveType = receiveType;
        }

        public String getSysUserId() {
            return sysUserId;
        }

        public void setSysUserId(String sysUserId) {
            this.sysUserId = sysUserId;
        }

        public String getSysUserName() {
            return sysUserName;
        }

        public void setSysUserName(String sysUserName) {
            this.sysUserName = sysUserName;
        }
    }

    public List<CustomerPublicBean.RowBean> getRows() {
        return rows;
    }

    public void setRows(List<CustomerPublicBean.RowBean> rows) {
        this.rows = rows;
    }

}
