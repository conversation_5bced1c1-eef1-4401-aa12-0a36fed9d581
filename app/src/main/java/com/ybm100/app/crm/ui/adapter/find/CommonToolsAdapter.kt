package com.ybm100.app.crm.ui.adapter.find

import android.net.Uri
import android.widget.ImageView
import com.fold.recyclyerview.BaseQuickAdapter
import com.fold.recyclyerview.BaseViewHolder
import com.xyy.utilslibrary.utils.GlideLoadUtils
import com.ybm100.app.crm.BuildConfig
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.MineMenuBean

class CommonToolsAdapter : BaseQuickAdapter<MineMenuBean.Row, BaseViewHolder>(R.layout.item_common_tools) {
    override fun convert(helper: BaseViewHolder, item: MineMenuBean.Row?) {
        item?.let {
            helper.setText(R.id.tv_name, it.name ?: "")
            val allowedChars = ":._-$,;~()/ "
            val urlEncoded = Uri.encode("${BuildConfig.MINE_MENU_ICON_URL}${it.icon ?: ""}", allowedChars)
            GlideLoadUtils.loadImgWithUrl(mContext, urlEncoded, helper.getView<ImageView>(R.id.iv_icon), R.drawable.icon_load_failed)
        }
    }

}