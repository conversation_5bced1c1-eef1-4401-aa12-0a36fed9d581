package com.ybm100.app.crm.model.pdf;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.contract.pdf.PdfDisplayContract;
import com.ybm100.app.crm.model.login.LoginModel;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;
import okhttp3.ResponseBody;

public class PdfDisplayModel extends BaseModel implements PdfDisplayContract.IPdfDisplayModel {

    @NonNull
    public static PdfDisplayModel newInstance() {
        return new PdfDisplayModel();
    }

    @Override
    public Observable<ResponseBody> downloadPdfFile(String url) {
        return RetrofitCreateHelper.createApi(ApiService.class).downloadFile(url);
    }
}
