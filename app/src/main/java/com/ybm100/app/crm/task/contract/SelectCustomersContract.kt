package com.ybm100.app.crm.task.contract

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.task.bean.ConfirmRecommendationBean
import com.ybm100.app.crm.task.bean.SelectCustomersBean
import io.reactivex.Observable


class SelectCustomersContract {
    interface ISelectCustomersView : IBaseActivity {
        fun onGetCustomerListSuccess(data: RequestBaseBean<SelectCustomersBean?>?, isRefresh: Boolean, isLastPage: Boolean)
        fun onGetCustomerListFail()
        fun onConfirmRecommendationSuccess(data: RequestBaseBean<ConfirmRecommendationBean?>?)
        fun onConfirmRecommendationFail()
    }

    interface ISelectCustomersModel : IBaseModel {
        fun getCustomerList(queryMap: Map<String, String>): Observable<RequestBaseBean<SelectCustomersBean?>?>
        fun confirmRecommendation(queryMap: Map<String, String>): Observable<RequestBaseBean<ConfirmRecommendationBean?>?>
    }
}