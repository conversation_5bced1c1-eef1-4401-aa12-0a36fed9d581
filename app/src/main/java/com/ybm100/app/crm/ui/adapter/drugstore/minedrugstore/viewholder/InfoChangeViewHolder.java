package com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore.viewholder;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.xyy.utilslibrary.utils.TimeUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.UpdateLogBean;
import com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore.infoadapter.InfoChangeListAdapter;
import com.ybm100.app.crm.utils.FormatUtils;

/**
 * <AUTHOR>
 */
public class InfoChangeViewHolder extends RecyclerView.ViewHolder {

    private final TextView tvInfoChangeOperatorName;
    private final TextView tvInfoChangeOperatorSerialNumber;
    private final TextView tvInfoChangeOperatorTime;
    private final RecyclerView rvInfoOperatorChangeList;
    private final TextView tvTableNameChange;
    private final TextView tvTableNameChangeType;

    public InfoChangeViewHolder(@NonNull View itemView) {
        super(itemView);
        tvInfoChangeOperatorName = itemView.findViewById(R.id.tv_info_change_operator_name);
        tvInfoChangeOperatorSerialNumber = itemView.findViewById(R.id.tv_info_change_operator_serial_number);
        tvInfoChangeOperatorTime = itemView.findViewById(R.id.tv_info_change_operator_time);
        rvInfoOperatorChangeList = itemView.findViewById(R.id.rv_info_operator_change_list);
        tvTableNameChange = itemView.findViewById(R.id.tv_table_name_change);
        tvTableNameChangeType = itemView.findViewById(R.id.tv_table_name_change_type);
    }

    public void bindChangeData(UpdateLogBean updateLogBean, Context context) {
        rvInfoOperatorChangeList.setLayoutManager(new LinearLayoutManager(context));
        tvInfoChangeOperatorName.setText(FormatUtils.textFormat(updateLogBean.getSysUserName()));
        tvTableNameChange.setText(FormatUtils.textFormat(updateLogBean.getTableNameText()));
        tvInfoChangeOperatorTime.setText(TimeUtils.millis2String(updateLogBean.getCreateTime(), TimeUtils.DATA_FORMAT_YMDHMS_2));

        if (TextUtils.isEmpty(updateLogBean.getJobNumber())) {
            tvInfoChangeOperatorSerialNumber.setText("");
        } else {
            tvInfoChangeOperatorSerialNumber.setText("(".concat(updateLogBean.getJobNumber()).concat(")"));
        }
        if (TextUtils.isEmpty(updateLogBean.getMethodTypeName())) {
            tvTableNameChangeType.setVisibility(View.GONE);
        } else {
            tvTableNameChangeType.setVisibility(View.VISIBLE);
            tvTableNameChangeType.setText("(".concat(updateLogBean.getMethodTypeName()).concat(")"));
        }
        InfoChangeListAdapter changeListAdapter = new InfoChangeListAdapter(R.layout.item_info_change_text, updateLogBean.getLogItems());
        rvInfoOperatorChangeList.setAdapter(changeListAdapter);
    }
}
