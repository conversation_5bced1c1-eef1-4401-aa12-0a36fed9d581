package com.ybm100.app.crm.bean.drugstore;

import com.ybm100.app.crm.bean.goods.PopListBean;

import java.util.ArrayList;

/**
 * @author: zcj
 * @time:2020/4/1. Description:
 */
public class PrivateListFilterBean {

    private ArrayList<PopListBean> customerType;
    private ArrayList<PopListBean> levelType;
    private ArrayList<PopListBean> licenseType;
    private ArrayList<PopListBean> sortType;

    public ArrayList<PopListBean> getCustomerType() {
        return customerType;
    }

    public void setCustomerType(ArrayList<PopListBean> customerType) {
        this.customerType = customerType;
    }

    public ArrayList<PopListBean> getLevelType() {
        return levelType;
    }

    public void setLevelType(ArrayList<PopListBean> levelType) {
        this.levelType = levelType;
    }

    public ArrayList<PopListBean> getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(ArrayList<PopListBean> licenseType) {
        this.licenseType = licenseType;
    }

    public ArrayList<PopListBean> getSortType() {
        return sortType;
    }

    public void setSortType(ArrayList<PopListBean> sortType) {
        this.sortType = sortType;
    }


}
