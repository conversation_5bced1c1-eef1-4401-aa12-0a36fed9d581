package com.ybm100.app.crm.bean.drugstore;

import java.io.Serializable;
import java.util.List;

/**
 * author :lx
 * date 2018/12/25.
 * email： <EMAIL>
 */
public class BaseInfo implements Serializable {


    /**
     * id : 16
     * syncNo : SYNC16
     * realName : 武汉可仁堂大药房有限公司
     * mobile : ***********
     * password : null
     * email :
     * nickname : 席彪
     * status : 1
     * type : null
     * createTime : 1461637075000
     * updateTime : null
     * authcode :
     * licenseStatus
     * <p>
     * : license_uncommitted(1,"资质未提交"),
     * license_committed(2,"资质已提交"),
     * license_expired(3,"首营纸质资质未回收"),
     * license_auditing(4,"资质已通过");
     * <p>
     * licenseText : 资质已通过
     * skLicenseStatus : null
     * sysUserId : 2049
     * sysUserName :
     * jobNumber :
     * checkcode : null
     * merchantType : 2
     * registerSource : 0
     * activeSource : 0
     * activeTime : null
     * businessType : 1
     * businessTypeName : 个体药店
     * province : null
     * city : null
     * district : null
     * address : null
     * registerCode : null
     * registerBranch : null
     * defaultContactor : null
     * defaultAddress : 天顺园小区302栋2单元1层1号 027-83311426
     * ids : null
     * statusName : 已激活
     * lastLoginDate : null
     * createStartTime : null
     * createEndTime : null
     * bizLastLoginTime : null
     * identity : 16
     * loginName : ***********
     * searchConditon : null
     * sysRealName : 帅裕欢
     * sysJobNumber : 80081111
     * registerCodes : null
     * merchantId : null
     * invoiceMailAddress : null
     * mobileBackUp : null
     * isClaim : null
     * provinceCode : null
     * cityCode : null
     * areaCode : null
     * invoiceType : null
     * taxNum : null
     * kfPhone : 400-0505-111
     * groupIdArray : null
     * keyword : null
     * merchantContact : []
     * branchList : null
     * syncAddress : null
     * lon : 114.695979
     * lat : 39.311036
     * statuses : null
     * registerStatus : null
     * businessTypes : null
     * findClientId : null
     * findClientText : null
     * startCreateTime : null
     * endCreateTime : null
     * currentLevel : null
     * currentLevelName : null
     * isBlackRemark : null
     * isBlackRemarkName : null
     * merchantBasicInfo : {"fkTbMerchantId":16,"updateTime":1543590611000,"currentLevel":null,"areaSize":103,"sku":1450,"customerType":"老人","purchaseWay":"九州通，药师帮","clerkNum":"2","merchantDemand":"账期","remark":"推江中","syncNo":null,"isBlackRemark":0,"monthlySales":0,"medicalInsurance":-1,"monthBuyAmt":"","registerAmt":"","aroundEnv":"","buyFrequency":"","buySkus":0,"frameVarieties":"","currentLevelName":null,"id":null,"isAcceptSB":null}
     * isSalesVolumeMonth : null
     * hasTrace : null
     * contactMobile : null
     * contactName : null
     * registerAmt : null
     * sysUserMobile : null
     * isLinkSea : null
     * skuList : null
     * lastLoginTime : null
     * merchantId
     */

    private String id;
    private String realName;
    private String defaultAddress;
    private String customerName;
    private String customerName4DB; //ec库中的注册名称
    private String address;
    private String poiLatitude;
    private String poiLongitude;
    //资质类型
    private String firstLicenseType;
    private String customerType;
    private String customerTypeName;//客户类型名称
    private String ecAddress;//药帮忙注册时的门店地址
    private String ecCustomerName;//药帮忙注册时的门店名称
    private int licenseStatus;
    private List<MerchantContact> merchantContact;
    private List<MerchantContact> contactList;
    private Register register;
    private int registerFlag;
    private CrmMerchantBasicInfoBean crmMerchantBasicInfo;
    private long createTime;
    private String nickname;
    private String mobile;
    private String businessTypeName;
    private String statusName;
    private String authcode;
    private String licenseText;
    private String sysRealName;
    private String sysJobNumber;
    private String poiId;
    private String poiMobilePhone;
    private String merchantId;
    private boolean poiAuditing;//poi是否在审核中
    private int poiAuditStatus;
    private String combName;//所属蜂窝名称
    private int unbindFlag;//是否可释放1是，2否

    private int poiLicenseStatus;
    private int poiRejectedReasonType;
    private String poiRejectedReason;
    private String poiAuditId;

    private List<SkuCollect> bindSkuCollect;

    public List<SkuCollect> getBindSkuCollect() {
        return bindSkuCollect;
    }

    public void setBindSkuCollect(List<SkuCollect> bindSkuCollect) {
        this.bindSkuCollect = bindSkuCollect;
    }

    public int getPoiLicenseStatus() {
        return poiLicenseStatus;
    }

    public void setPoiLicenseStatus(int poiLicenseStatus) {
        this.poiLicenseStatus = poiLicenseStatus;
    }

    public int getPoiRejectedReasonType() {
        return poiRejectedReasonType;
    }

    public void setPoiRejectedReasonType(int poiRejectedReasonType) {
        this.poiRejectedReasonType = poiRejectedReasonType;
    }

    public String getPoiRejectedReason() {
        return poiRejectedReason;
    }

    public void setPoiRejectedReason(String poiRejectedReason) {
        this.poiRejectedReason = poiRejectedReason;
    }

    public String getPoiAuditId() {
        return poiAuditId;
    }

    public void setPoiAuditId(String poiAuditId) {
        this.poiAuditId = poiAuditId;
    }

    public boolean isPoiAuditing() {
        return poiAuditing;
    }

    public String getCombName() {
        return combName;
    }

    public void setCombName(String combName) {
        this.combName = combName;
    }

    public int getUnbindFlag() {
        return unbindFlag;
    }

    public void setUnbindFlag(int unbindFlag) {
        this.unbindFlag = unbindFlag;
    }

    public String getPoiMobilePhone() {
        return poiMobilePhone;
    }

    public boolean getPoiAuditing() {
        return poiAuditing;
    }

    public int getPoiAuditStatus() {
        return poiAuditStatus;
    }

    public void setPoiAuditStatus(int poiAuditStatus) {
        this.poiAuditStatus = poiAuditStatus;
    }

    public void setPoiAuditing(boolean poiAuditing) {
        this.poiAuditing = poiAuditing;
    }

    public void setPoiMobilePhone(String poiMobilePhone) {
        this.poiMobilePhone = poiMobilePhone;
    }

    public String getPoiId() {
        return poiId;
    }

    public void setPoiId(String poiId) {
        this.poiId = poiId;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getLicenseText() {
        return licenseText;
    }

    public void setLicenseText(String licenseText) {
        this.licenseText = licenseText;
    }

    public String getSysRealName() {
        return sysRealName;
    }

    public void setSysRealName(String sysRealName) {
        this.sysRealName = sysRealName;
    }

    public String getSysJobNumber() {
        return sysJobNumber;
    }

    public void setSysJobNumber(String sysJobNumber) {
        this.sysJobNumber = sysJobNumber;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRealName() {
        return realName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public int getRegisterFlag() {
        return registerFlag;
    }

    public void setRegisterFlag(int registerFlag) {
        this.registerFlag = registerFlag;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getDefaultAddress() {
        return defaultAddress;
    }

    public void setDefaultAddress(String defaultAddress) {
        this.defaultAddress = defaultAddress;
    }

    public String getPoiLatitude() {
        return poiLatitude;
    }

    public void setPoiLatitude(String poiLatitude) {
        this.poiLatitude = poiLatitude;
    }

    public String getPoiLongitude() {
        return poiLongitude;
    }

    public void setPoiLongitude(String poiLongitude) {
        this.poiLongitude = poiLongitude;
    }

    public int getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(int licenseStatus) {
        this.licenseStatus = licenseStatus;
    }


    public String getFirstLicenseType() {
        return firstLicenseType;
    }

    public void setFirstLicenseType(String firstLicenseType) {
        this.firstLicenseType = firstLicenseType;
    }

    public List<MerchantContact> getMerchantContact() {
        return merchantContact;
    }

    public void setMerchantContact(List<MerchantContact> merchantContact) {
        this.merchantContact = merchantContact;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public List<MerchantContact> getContactList() {
        return contactList;
    }

    public void setContactList(List<MerchantContact> contactList) {
        this.contactList = contactList;
    }

    public Register getRegister() {
        return register;
    }

    public void setRegister(Register register) {
        this.register = register;
    }

    public CrmMerchantBasicInfoBean getCrmMerchantBasicInfo() {
        return crmMerchantBasicInfo;
    }

    public void setCrmMerchantBasicInfo(CrmMerchantBasicInfoBean crmMerchantBasicInfo) {
        this.crmMerchantBasicInfo = crmMerchantBasicInfo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getBusinessTypeName() {
        return businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getAuthcode() {
        return authcode;
    }

    public void setAuthcode(String authcode) {
        this.authcode = authcode;
    }

    public String getCustomerName4DB() {
        return customerName4DB;
    }

    public void setCustomerName4DB(String customerName4DB) {
        this.customerName4DB = customerName4DB;
    }

    public String getCustomerTypeName() {
        return customerTypeName;
    }

    public void setCustomerTypeName(String customerTypeName) {
        this.customerTypeName = customerTypeName;
    }

    public String getEcAddress() {
        return ecAddress;
    }

    public void setEcAddress(String ecAddress) {
        this.ecAddress = ecAddress;
    }

    public String getEcCustomerName() {
        return ecCustomerName;
    }

    public void setEcCustomerName(String ecCustomerName) {
        this.ecCustomerName = ecCustomerName;
    }

    public static class MerchantBasicInfoBean implements Serializable {
        /**
         * fkTbMerchantId : 16
         * updateTime : 1543590611000
         * currentLevel : null
         * areaSize : 103
         * sku : 1450
         * customerType : 老人
         * purchaseWay : 九州通，药师帮
         * clerkNum : 2
         * merchantDemand : 账期
         * remark : 推江中
         * syncNo : null
         * isBlackRemark : 0
         * monthlySales : 0
         * medicalInsurance : -1
         * monthBuyAmt :
         * registerAmt :
         * aroundEnv :
         * buyFrequency :
         * buySkus : 0
         * frameVarieties :
         * currentLevelName : null
         * id : null
         * isAcceptSB : null
         */

        private int fkTbMerchantId;
        private long updateTime;
        private Object currentLevel;
        private String areaSize;
        private String sku;
        private String customerType;
        private String purchaseWay;
        private String clerkNum;
        private String merchantDemand;
        private String remark;
        private Object syncNo;
        private int isBlackRemark;
        private int medicalInsurance;
        private String monthBuyAmt;
        private String registerAmt;
        private String aroundEnv;
        private String buyFrequency;
        private String buySkus;
        private String frameVarieties;
        private String currentLevelName;
        private Object id;
        private Object isAcceptSB;

        public int getFkTbMerchantId() {
            return fkTbMerchantId;
        }

        public void setFkTbMerchantId(int fkTbMerchantId) {
            this.fkTbMerchantId = fkTbMerchantId;
        }

        public long getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(long updateTime) {
            this.updateTime = updateTime;
        }

        public Object getCurrentLevel() {
            return currentLevel;
        }

        public void setCurrentLevel(Object currentLevel) {
            this.currentLevel = currentLevel;
        }

        public String getAreaSize() {
            return areaSize;
        }

        public void setAreaSize(String areaSize) {
            this.areaSize = areaSize;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getCustomerType() {
            return customerType;
        }

        public void setCustomerType(String customerType) {
            this.customerType = customerType;
        }

        public String getPurchaseWay() {
            return purchaseWay;
        }

        public void setPurchaseWay(String purchaseWay) {
            this.purchaseWay = purchaseWay;
        }

        public String getClerkNum() {
            return clerkNum;
        }

        public void setClerkNum(String clerkNum) {
            this.clerkNum = clerkNum;
        }

        public String getMerchantDemand() {
            return merchantDemand;
        }

        public void setMerchantDemand(String merchantDemand) {
            this.merchantDemand = merchantDemand;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public Object getSyncNo() {
            return syncNo;
        }

        public void setSyncNo(Object syncNo) {
            this.syncNo = syncNo;
        }

        public int getIsBlackRemark() {
            return isBlackRemark;
        }

        public void setIsBlackRemark(int isBlackRemark) {
            this.isBlackRemark = isBlackRemark;
        }

        public int getMedicalInsurance() {
            return medicalInsurance;
        }

        public void setMedicalInsurance(int medicalInsurance) {
            this.medicalInsurance = medicalInsurance;
        }

        public String getMonthBuyAmt() {
            return monthBuyAmt;
        }

        public void setMonthBuyAmt(String monthBuyAmt) {
            this.monthBuyAmt = monthBuyAmt;
        }

        public String getRegisterAmt() {
            return registerAmt;
        }

        public void setRegisterAmt(String registerAmt) {
            this.registerAmt = registerAmt;
        }

        public String getAroundEnv() {
            return aroundEnv;
        }

        public void setAroundEnv(String aroundEnv) {
            this.aroundEnv = aroundEnv;
        }

        public String getBuyFrequency() {
            return buyFrequency;
        }

        public void setBuyFrequency(String buyFrequency) {
            this.buyFrequency = buyFrequency;
        }

        public String getBuySkus() {
            return buySkus;
        }

        public void setBuySkus(String buySkus) {
            this.buySkus = buySkus;
        }

        public String getFrameVarieties() {
            return frameVarieties;
        }

        public void setFrameVarieties(String frameVarieties) {
            this.frameVarieties = frameVarieties;
        }

        public String getCurrentLevelName() {
            return currentLevelName;
        }

        public void setCurrentLevelName(String currentLevelName) {
            this.currentLevelName = currentLevelName;
        }

        public Object getId() {
            return id;
        }

        public void setId(Object id) {
            this.id = id;
        }

        public Object getIsAcceptSB() {
            return isAcceptSB;
        }

        public void setIsAcceptSB(Object isAcceptSB) {
            this.isAcceptSB = isAcceptSB;
        }
    }
}

