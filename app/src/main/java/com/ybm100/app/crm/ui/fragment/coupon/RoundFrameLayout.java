package com.ybm100.app.crm.ui.fragment.coupon;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;

import com.xyy.common.widget.RoundViewDelegate;

/**
 * 用于需要圆角矩形框背景的FrameLayout的情况,减少直接使用FrameLayout时引入的shape资源文件
 */
public class RoundFrameLayout extends FrameLayout {
    private final RoundViewDelegate delegate;

    public RoundFrameLayout(Context context) {
        this(context, null);
    }

    public RoundFrameLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        delegate = new RoundViewDelegate(this, context, attrs);
    }

    public RoundFrameLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        delegate = new RoundViewDelegate(this, context, attrs);
    }

    /**
     * use delegate to set attr
     */
    public RoundViewDelegate getDelegate() {
        return delegate;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (delegate.isWidthHeightEqual() && getWidth() > 0 && getHeight() > 0) {
            int max = Math.max(getWidth(), getHeight());
            int measureSpec = MeasureSpec.makeMeasureSpec(max, MeasureSpec.EXACTLY);
            super.onMeasure(measureSpec, measureSpec);
            return;
        }

        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (delegate.isRadiusHalfHeight()) {
            delegate.setCornerRadius(getHeight() / 2);
        } else {
            delegate.setBgSelector();
        }
    }

    public void setStrokeWidth(float width) {
        delegate.setStrokeWidth(width);
    }

    @Override
    public void setBackgroundColor(int color) {
        delegate.setBackgroundColor(color);
    }

    public void setBackgroundColors(int[] backgroundColors) {
        delegate.setBackgroundColors(backgroundColors);
    }

    public void setStrokeColor(int color) {
        delegate.setStrokeColor(color);
    }
}
