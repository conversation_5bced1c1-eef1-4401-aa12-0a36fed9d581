package com.ybm100.app.crm.goods.ui.widget

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.renderer.YAxisRenderer
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.ViewPortHandler

class CustomerYAxisRenderer(viewPortHandler: ViewPortHandler?, yAxis: YAxis?, trans: Transformer?) : YAxis<PERSON>enderer(viewPortHandler, yAxis, trans) {
    private val mEdgeGridColor: Int = Color.parseColor("#dddddd")
    private val mEdgeGridPaint: Paint = Paint()
    override fun renderGridLines(c: Canvas) {
        super.renderGridLines(c)
        if (!mYAxis.isEnabled) return
        if (mYAxis.isDrawGridLinesEnabled) {
            val clipRestoreCount = c.save()
            c.clipRect(gridClippingRect)
            val positions = transformedPositions
            mGridPaint.color = mYAxis.gridColor
            mGridPaint.strokeWidth = mYAxis.gridLineWidth
            mGridPaint.pathEffect = mYAxis.gridDashPathEffect

            mEdgeGridPaint.color = mEdgeGridColor
            mEdgeGridPaint.strokeWidth = mYAxis.gridLineWidth
            mEdgeGridPaint.pathEffect = mYAxis.gridDashPathEffect


            val gridLinePath = mRenderGridLinesPath
            gridLinePath.reset()

            // draw the grid
            var i = 0
            while (i < positions.size) {

                // 最后一个和第一个颜色不一样
                if (i == 0 || i + 2 >= positions.size) {
                    c.drawPath(linePath(gridLinePath, i, positions), mEdgeGridPaint)
                } else {
                    c.drawPath(linePath(gridLinePath, i, positions), mGridPaint)
                }
                gridLinePath.reset()
                i += 2
            }
            c.restoreToCount(clipRestoreCount)
        }
        if (mYAxis.isDrawZeroLineEnabled) {
            drawZeroLine(c)
        }
    }
}