package com.ybm100.app.crm.ui.fragment.lzcustomer

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import com.baidu.location.BDLocation
import com.just.ynbweb.bean.LocationInfo
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.util.ToastUtils
import com.xyy.common.widget.GridSpacingItemDecoration
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.rxbus.RxBus
import com.xyy.utilslibrary.utils.DisplayUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.api.ApiUrl
import com.ybm100.app.crm.bean.lzcustomer.LzPublicDetialBean
import com.ybm100.app.crm.constant.DrugstoreConstants
import com.ybm100.app.crm.constant.RxBusCode
import com.ybm100.app.crm.contract.lzcustomer.LzPublicDetailContract
import com.ybm100.app.crm.doraemon.ynb.YNBHybridActivity
import com.ybm100.app.crm.order.adapter.PublicDetailPicAdapter
import com.ybm100.app.crm.permission.PermissionUtil
import com.ybm100.app.crm.presenter.lzcustomer.LzPublicDetailPresenter
import com.ybm100.app.crm.ui.activity.lbs.LocationManager
import com.ybm100.app.crm.utils.FormatUtils
import com.ybm100.app.crm.utils.SnowGroundUtils
import com.ybm100.app.crm.widget.drug.DialogMan
import com.ybm100.app.crm.widget.popwindow.CustomerDetailPopup
import kotlinx.android.synthetic.main.activity_lz_public_detial.*
import java.util.*

/**
 * @author: zcj
 * @time:2020/6/10.
 * Description:
 */
class LzPublicDetailActivity : BaseMVPCompatActivity<LzPublicDetailPresenter>(), LzPublicDetailContract.ILzPublicDetailView {
    override fun initPresenter(): BasePresenter<*, *> = LzPublicDetailPresenter.newInstance()

    override fun getLayoutId(): Int = R.layout.activity_lz_public_detial
    private var id: String? = null
    private var poiId: String? = null
    private var bar: DefaultNavigationBar? = null
    private var locationListener: LocationManager.LocationListener? = null
    private var locationInfo: LocationInfo? = null
    override fun initView(savedInstanceState: Bundle?) {
        id = intent.getStringExtra(DrugstoreConstants.INTENT_KEY_OPENSEAID)
        mPresenter.searchOpenSeaDetail(id)
        initLocationListener()
    }

    override fun initHead(): AbsNavigationBar<*> {
        bar = DefaultNavigationBar.Builder(this).setTitle("客户详情").builder()
        return bar!!
    }

    override fun showNetError() {
    }

    override fun searchOpenSeaDetailSuccess(baseBean: LzPublicDetialBean?) {
        tv_customer_name.text =
                if (TextUtils.isEmpty(baseBean?.ecCustomerName))
                    FormatUtils.textFormat(baseBean?.customerName)
                else
                    FormatUtils.textFormat(baseBean?.ecCustomerName)
        tv_bd.text = FormatUtils.textFormat(baseBean?.linkman)
        tv_phone.text = FormatUtils.textFormat(baseBean?.phone)
        //详细地址
        when (baseBean?.registerFlag) { //详细地址
            2 -> tv_detail_address.text = FormatUtils.textFormat(baseBean.address)
            1 -> tv_detail_address.text = FormatUtils.textFormat(baseBean.ecAddress)
            else -> tv_detail_address.text = "-"
        }
        tv_detail_type.text = FormatUtils.textFormat(baseBean?.customerTypeName)
        tv_remark.text = FormatUtils.textFormat(baseBean?.remark)
        //门头照
        val linearLayoutManager = androidx.recyclerview.widget.GridLayoutManager(this, 3)
        val itemDecoration = GridSpacingItemDecoration(3, resources.getDimensionPixelOffset(R.dimen.dp10), resources.getDimensionPixelOffset(R.dimen.dp10), true)
        recycle_view_photo.addItemDecoration(itemDecoration)
        recycle_view_photo.layoutManager = linearLayoutManager
        if (!TextUtils.isEmpty(baseBean?.poiImageUrl)) {
            val imageStr: Array<String>? = baseBean?.poiImageUrl?.split(",")?.toTypedArray()
            if (imageStr.isNullOrEmpty()) return
            val imageInfo: List<String> = ArrayList(listOf(*imageStr))
            val adapter = PublicDetailPicAdapter(R.layout.item_image_door_photo, imageInfo, false, linearLayoutManager)
            recycle_view_photo.adapter = adapter
        }
        //是否被领取 1是 2否 未被认领展示认领按钮
        if (baseBean?.receiveType == 2) {
            layout_claim.visibility = View.VISIBLE
            tv_customer_status.text = "未认领"
            tv_customer_status.setTextColor(ContextCompat.getColor(this, R.color.color_red_FC132F))
        } else {
            layout_claim.visibility = View.GONE
            tv_customer_status.text = "已认领"
            tv_customer_status.setTextColor(ContextCompat.getColor(this, R.color.text_color_35C561))
        }

        tv_claim.setOnClickListener {
            DialogMan.Builder(mContext).setListener(object : DialogMan.OnClickListener {
                override fun confirm(dialogMan: DialogMan): Boolean {
                    mPresenter.receive(id)
                    return true
                }

                override fun cancel(dialogMan: DialogMan): Boolean {
                    return true
                }
            }).setTitle("确认是否认领").setConfirmText("确认")
                    .setCancelText("取消")
                    .setCancelTextColor(ContextCompat.getColor(mContext, R.color.text_color_FF9494A6))
                    .setConfirmTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561))
                    .setCancelable(false)
                    .buildAndShow()
        }
        //未签约并且未注册  //1已签约 2未签约  //1已注册  2未注册
        if (baseBean?.signStatus == "2" && baseBean.registerFlag == 2) {
            poiId = baseBean.poiId
            bar!!.rightImgView.setImageResource(R.drawable.ic_customer_more)
            bar!!.rightImgView.setOnClickListener { v: View ->
                rightOnClick(baseBean, v)
            }
        } else {
            bar!!.rightImgView.setImageBitmap(null)
        }
    }

    private fun rightOnClick(baseBean: LzPublicDetialBean, v: View) {
        when (baseBean.poiAuditStatus) {
            1 -> {
                ToastUtils.showShort("客户信息审核中，无法提交审核")
            }
            3 -> {
                ToastUtils.showShort("客户信息审核驳回，无法提交审核")
            }
            2 -> {
                val dataList = listOf(*resources.getStringArray(R.array.customer_detail_menu_post))
                val detailPopup = CustomerDetailPopup(this, dataList)
                detailPopup.setBackgroundDrawable(R.drawable.pop_bg_right)
                detailPopup.setOnPopItemClickListener {
                    val map = HashMap<String, String>()
                    map["PoiId"] = poiId!!
                    SnowGroundUtils.track("Event-LzPublicDetail-UploadError", map)
                    postError()
                }
                val location = IntArray(2)
                v.getLocationOnScreen(location)
                detailPopup.showPopupWindow((location[0] + v.width + DisplayUtils.dp2px(15f)),
                        location[1] + v.height + DisplayUtils.dp2px(5f))
            }
        }
    }

    private fun postError() {
        LocationManager.getInstance().locationPermissions(this, locationListener, true, PermissionUtil.OnCancelCallBack { })
    }

    //定位监听
    fun initLocationListener() {
        locationListener = LocationManager.LocationListener { bd: BDLocation ->
            locationInfo = LocationInfo()
            locationInfo?.latitude = bd.latitude.toString()
            locationInfo?.longitude = bd.longitude.toString()
            val url = ApiUrl.getDataPostError() + "?poiId=" + poiId + "&" + ApiUrl.getSourceType()
            YNBHybridActivity.jumpYnb(this, url, locationInfo)
        }
    }


    override fun onPause() {
        super.onPause()
        LocationManager.getInstance().unRegisterLocationListener(locationListener)
        LocationManager.getInstance().stopLocation()
    }

    override fun receiveSuccess(requestBaseBean: RequestBaseBean<*>?) {
        if (requestBaseBean == null) {
            return
        }
        if (requestBaseBean.isSuccess) {
            ToastUtils.showLong("认领成功")
            RxBus.get().send(RxBusCode.RX_BUS_UPDATE_PRIVATE_LIST)
            finish()
        } else if (!requestBaseBean.isSuccess && (requestBaseBean.errorCode == 405 || requestBaseBean.errorCode == 406)) {
            DialogMan.Builder(mContext).setListener(object : DialogMan.OnClickListener {
                override fun confirm(dialogMan: DialogMan): Boolean {
                    return true
                }

                override fun cancel(dialogMan: DialogMan): Boolean {
                    return true
                }
            }).setTitle(requestBaseBean.errorMsg).setConfirmText("确认")
                    .setConfirmTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561))
                    .setCancelable(false)
                    .buildAndShow()
        } else {
            ToastUtils.showLong(requestBaseBean.errorMsg)
        }
    }
}