package com.ybm100.app.crm.bean.schedule;

import java.util.ArrayList;
import java.util.List;

/**
 * Author ： LoveNewsweetheart
 * Date:2019/1/13
 */
public class VisitCauseOptionalBean {

    private String name;
    private String id;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public VisitCauseOptionalBean(String name, String id) {
        this.name = name;
        this.id = id;
    }

    private static final List<VisitCauseOptionalBean> visitCauseOptinalList = new ArrayList<>();

    static {
        visitCauseOptinalList.add(new VisitCauseOptionalBean("开发新客户", "1"));
        visitCauseOptinalList.add(new VisitCauseOptionalBean("客情维护", "2"));
        visitCauseOptinalList.add(new VisitCauseOptionalBean("新品推介", "3"));
        visitCauseOptinalList.add(new VisitCauseOptionalBean("活动促销", "4"));
        visitCauseOptinalList.add(new VisitCauseOptionalBean("商品维价", "5"));
//        visitCauseOptinalList.add(new VisitCauseOptionalBean("做任务", "8"));
//        visitCauseOptinalList.add(new VisitCauseOptionalBean("其他", "6"));
        /**
         * 2.9.0 修改
         * 入参visitReason枚举去掉8、做任务,6、其他，增加9、资质回收
         */
        visitCauseOptinalList.add(new VisitCauseOptionalBean("资质回收", "9"));
        /**
         * 3.0新增 老客唤醒、售后处理
         */
        visitCauseOptinalList.add(new VisitCauseOptionalBean("老客唤醒", "10"));
        visitCauseOptinalList.add(new VisitCauseOptionalBean("售后处理", "11"));
    }

    public static List<VisitCauseOptionalBean> getAllTypes() {
        return visitCauseOptinalList;
    }
}
