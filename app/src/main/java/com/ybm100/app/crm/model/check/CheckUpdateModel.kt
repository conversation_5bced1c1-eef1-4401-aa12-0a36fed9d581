package com.ybm100.app.crm.model.check

import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.bean.update.VersionInfo
import com.ybm100.app.crm.contract.check.CheckUpdateContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import io.reactivex.Observable

/**
 * @author: zcj
 * @time:2019/12/2.
 *
 * Description:
 */
class CheckUpdateModel : CheckUpdateContract.ICheckUpdateModel {
    override fun checkUpdate(): Observable<RequestBaseBean<VersionInfo>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).checkUpdate()
                .compose(RxHelper.rxSchedulerHelper())
    }
}