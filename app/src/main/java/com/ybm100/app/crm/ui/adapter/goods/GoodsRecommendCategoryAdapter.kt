package com.ybm100.app.crm.ui.adapter.goods

import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.goods.GoodsCategoryBean
import com.ybm100.app.crm.constant.Constants

/**
 * @author: zcj
 * @time:2019/11/4.
 *
 * Description:
 */
class GoodsRecommendCategoryAdapter(var mList: ArrayList<GoodsCategoryBean>?, var type: Int) : BaseQuickAdapter<GoodsCategoryBean, BaseViewHolder>(R.layout.item_pop_goods_recommend, mList) {

    override fun convert(helper: BaseViewHolder, item: GoodsCategoryBean) {
        val content = helper.getView<TextView>(R.id.tv_content)
        val ivSelect = helper.getView<ImageView>(R.id.iv_select)
        val layout = helper.getView<ConstraintLayout>(R.id.layout)
        if (type == Constants.GoodsRecommendCheck.TYPE_CATEGORY_1) {
            content.text = item.categoryName1
            if (item.isSelected) {
                layout.setBackgroundColor(ContextCompat.getColor(mContext, R.color.white))
                content.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561))
            } else {
                content.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_333333))
                layout.setBackgroundColor(ContextCompat.getColor(mContext, R.color.color_f7f7f8))
            }
        } else {
            content.text = item.categoryName2
            //分类2才展示对勾
            if (item.isSelected) {
                ivSelect.visibility = View.VISIBLE
                content.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561))
            } else {
                ivSelect.visibility = View.GONE
                content.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_333333))
            }
        }
    }

    fun selectItem(position: Int): Boolean {
        var change = false
        if (mList == null || mList.isNullOrEmpty()) {
            return false
        } else {
            //单选
            if (type == Constants.GoodsRecommendCheck.TYPE_CATEGORY_1) {
                for (i in mList!!.indices) {
                    if (i == position) {
                        mList!![i].isSelected = true
                        change = true
                    } else {
                        mList!![i].isSelected = false
                    }
                }
            } else if (type == Constants.GoodsRecommendCheck.TYPE_CATEGORY_2) {
                if (position == 0) {
                    val select = mList!![position].isSelected
                    if (!select) {
                        for (i in mList!!.indices) {
                            mList!![i].isSelected = i == 0
                        }
                    } else {
                        mList!![0].isSelected = false
                    }
                } else {
                    val select = mList!![position].isSelected
                    mList!![position].isSelected = !select
                    mList!![0].isSelected = false
                }
            }
            notifyDataSetChanged()
        }
        return change
    }
}