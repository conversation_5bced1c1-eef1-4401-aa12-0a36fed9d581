package com.ybm100.app.crm.task.adapter

import android.widget.ProgressBar
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm100.app.crm.R
import com.ybm100.app.crm.task.bean.TeamTaskBean
import com.ybm100.app.crm.utils.SpannableStringUtils
import java.text.DecimalFormat


class TeamTaskAdapter : BaseQuickAdapter<TeamTaskBean.Row, BaseViewHolder>(R.layout.item_team_task) {

    override fun convert(helper: BaseViewHolder, item: TeamTaskBean.Row?) {
        item?.run {
            val formatTaskRate = DecimalFormat("#.##").format(rate)
            helper.setText(R.id.tv_name, "${name ?: ""}")
                    .setText(R.id.tv_sales_degree_of_completion, SpannableStringUtils.highlightMiddle(mContext, R.color.color_00B377, "", "${achieveGoal ?: ""}", "/${goal ?: ""}"))
                    .setText(R.id.tv_progress_bar_value, SpannableStringUtils.highlightEnd(mContext, R.color.color_00B377, "达成", "${formatTaskRate ?: ""}%"))
            helper.getView<ProgressBar>(R.id.progress_bar).progress = rate?.toInt() ?: 0
            if (goal?.equals("--") == true) {
                helper.setGone(R.id.progress_bar, false)
                helper.setGone(R.id.tv_progress_bar_value, false)
            } else {
                helper.setGone(R.id.progress_bar, true)
                helper.setGone(R.id.tv_progress_bar_value, true)
            }
        }
    }

}