package com.ybm100.app.crm.bean.drugstore.minedrugstore;

import java.io.Serializable;
import java.util.List;

/**
 * author :lx
 * date 2019/1/7.
 * email： <EMAIL>
 */
public class CartGroupBean implements Serializable {
    /**
     * companyName	商品公司	string	@mock=$order('武汉小药药医药科技有限公司','武汉小药药医药科技有限公司','')
     * selectStatus	分组选中状态：1是 0 否	number	@mock=$order(1,1,1)
     * sorted	    购物车商品信息	array<object>
     * title	    标题	string
     * totalAmount	number	@mock=$order(0,0,0)
     * type	        1-满减价，2-满送赠品，3-满送优惠券, 9-不参与活动，10-套餐	number	@mock=$order(10,9,)
     * valid	    是否可用（1 是 0 否）	number	@mock=$order(1,1,0)
     */
    private String companyName;
    private int selectStatus;
    private String totalAmount;
    private String title;
    private int type;
    private int valid;
    private List<SortedBean> sorted;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public int getSelectStatus() {
        return selectStatus;
    }

    public void setSelectStatus(int selectStatus) {
        this.selectStatus = selectStatus;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public List<SortedBean> getSorted() {
        return sorted;
    }

    public void setSorted(List<SortedBean> sorted) {
        this.sorted = sorted;
    }
}
