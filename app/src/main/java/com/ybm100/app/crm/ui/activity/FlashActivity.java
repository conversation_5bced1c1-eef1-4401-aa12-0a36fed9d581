package com.ybm100.app.crm.ui.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.target.CustomViewTarget;
import com.bumptech.glide.request.transition.Transition;
import com.google.gson.Gson;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.xyy.flutter.container.container.ContainerRuntime;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.dialog.JYDialog;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.utilslibrary.utils.StringUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.home.ShowListBean;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.net.RetrofitCreateHelper;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.permission.PermissionUtil;
import com.ybm100.app.crm.ui.activity.login.LoginActivity;
import com.ybm100.app.crm.utils.Calendar.CalendarUtils;
import com.ybm100.app.crm.utils.GsonUtils;
import com.ybm100.app.crm.utils.SharedPrefManager;
import com.ybm100.app.crm.utils.SnowGroundUtils;

import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;


/**
 * 引导页
 */
public class FlashActivity extends BaseMVPCompatActivity {

    @BindView(R.id.btn_skip)
    Button tvCountDown;
    @BindView(R.id.constraint_layout)
    ConstraintLayout constraintLayout;
    /**
     * 取消进入主页
     */
    private boolean mIsCancel;
    /**
     * 倒计时
     */
    private final int mTime = 3;
    /**
     * 权限弹框
     */
    private JYDialog permissionDialog;
    private final CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    private final Handler mHandler = new Handler();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // 全屏
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        setTheme(R.style.FlashTheme1);
        super.onCreate(savedInstanceState);
        if ((getIntent().getFlags() & Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0) {
            finish();
        } else {
            requestPermissions();
            refreshTicketStatus();
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mCompositeDisposable.clear();
        mHandler.removeCallbacksAndMessages(null);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_flash;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
    }

    private void refreshTicketStatus() {
        if (TextUtils.isEmpty(SharedPrefManager.getInstance().getUserInfo().getToken())) return;
        Disposable subscribe = RetrofitCreateHelper.createApi(ApiService.class).refreshTicketStatus()
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(this) {
                    @Override
                    public void onSuccess(RequestBaseBean requestBaseBean) {

                    }
                }, new SimpleErrorConsumer(this));
        addDisposable(subscribe);
    }

    /**
     * 授权成功后开启应用
     */
    private void openApplication() {
        if (permissionDialog != null) {
            permissionDialog.dismiss();
        }
        //未登录不请求广告接口
        if (SharedPrefManager.getInstance().isLogin()) {
            //获取缓存
            String cache = SharedPrefManager.getInstance().getString("showAd", "");
            //缓存不为空时展示缓存
            if (!TextUtils.isEmpty(cache)) {
                ShowListBean response = GsonUtils.fromJson(cache, ShowListBean.class);
                setShow(response);
            }
            //更新缓存
            Disposable subscribe = RetrofitCreateHelper.createApi(ApiService.class).queryShowList()
                    .compose(RxHelper.rxSchedulerHelper())
                    .subscribe(new SimpleSuccessConsumer<RequestBaseBean<ShowListBean>>(this) {

                        @Override
                        public void onSuccess(RequestBaseBean<ShowListBean> showListBeanRequestBaseBean) {
                            SharedPrefManager.getInstance().setString("showAd", new Gson().toJson(showListBeanRequestBaseBean.getData())).apply();
                                                    }
                    }, new SimpleErrorConsumer(this));
            addDisposable(subscribe);
        }
                //请求成功初始化倒计时
        initCountDown();
            }

    private void setShow(ShowListBean response) {
        //try catch一下防止阻塞
        try {
            for (int i = 0; response.getRows() != null && i < response.getRows().size(); i++) {
                if (CalendarUtils.getTimeStamp().equals(response.getRows().get(i).getShowDay())) {
                    String url = response.getRows().get(i).getUrl();
                    showUrl(url);
                } else {
                    saveCache(response.getRows().get(i).getUrl());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void saveCache(String url) {
        Glide.with(FlashActivity.this).load(url).diskCacheStrategy(DiskCacheStrategy.DATA).preload();
    }

    private void showUrl(String url) {
                mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (FlashActivity.this.isFinishing()
                        || FlashActivity.this.isDestroyed()) {
                    return;
                }
                Glide.with(FlashActivity.this)
                        .asDrawable()
                        .load(url)
                        .centerCrop()
                        .diskCacheStrategy(DiskCacheStrategy.DATA)
                        .into(new CustomViewTarget<ConstraintLayout, Drawable>(constraintLayout) {
                            @Override
                            public void onLoadFailed(@Nullable Drawable errorDrawable) {
                                constraintLayout.setBackground(getResources().getDrawable(R.drawable.flash_bg_new));
                            }

                            @Override
                            public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                                constraintLayout.setBackground(resource);
                            }

                            @Override
                            protected void onResourceCleared(@Nullable Drawable placeholder) {
                                constraintLayout.setBackground(getResources().getDrawable(R.drawable.flash_bg_new));
                            }
                        });
            }
        }, 500);
    }

    @Override
    public void onBackPressedSupport() {
        mIsCancel = true;
        setIsTransAnim(false);
        finish();
    }

    @OnClick(R.id.btn_skip)
    public void onClick(View v) {
        //点击跳过
        if (v.getId() == R.id.btn_skip) {
            mIsCancel = true;
            startHome();
        }
    }

    /**
     * 适配Android6.0申请权限
     */
    @SuppressLint("CheckResult")
    private void requestPermissions() {
        RxPermissions rxPermissions = new RxPermissions(FlashActivity.this);
        rxPermissions.request(Manifest.permission.READ_PHONE_STATE, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE).subscribe(granted -> {
            if (!granted) {
                // 未获取权限
                showPermissionDialog1(FlashActivity.this, getString(R.string.new_splash_need_permission), false);
            } else {
                openApplication();
            }
        });
    }

    /**
     * 显示授权提示框
     *
     * @param context      上下文
     * @param permission   权限名称
     * @param isCancelable 是否可以取消
     */
    public void showPermissionDialog1(final Context context, String permission, Boolean isCancelable) {
        permissionDialog = new JYDialog(context, null, isCancelable);
        permissionDialog.setContent(permission + "权限，请到 “应用信息 -> 权限” 中授予！");
        permissionDialog.setTitleIsVisible(false);
        permissionDialog.setRightButtonTextColor(ContextCompat.getColor(context, R.color.color_FF3E0A));
        permissionDialog.setRightButtonTextStyle();
        if (isCancelable) {
            permissionDialog.setLeftText("取消", arg0 -> permissionDialog.dismiss());
        } else {
            // 不可以点击外部取消，也不显示取消按钮
            permissionDialog.setShowOk(false);
        }
        permissionDialog.setRightText("去授权", v -> PermissionUtil.openAppSettingDetail(this)).show();
    }

    /**
     * 初始化倒计时
     */
    private void initCountDown() {
        Observable.interval(1, TimeUnit.SECONDS)
                .take(mTime)//计时次数
                .map(aLong -> {
                    return mTime - aLong;// 3-0 3-2 3-1
                })
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(new Observer<Long>() {

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }

                    @Override
                    public void onNext(Long value) {
                        if (tvCountDown != null) {
                            tvCountDown.setVisibility(View.VISIBLE);
                            tvCountDown.setText(StringUtils.isEmpty(String.valueOf(value)) ? "" : "跳过 " + value);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onComplete() {
                        if (!mIsCancel) {
                            startHome();
                        }
                    }
                });
    }

    /**
     * 进入首页
     */
    private void startHome() {
        //判断用户信息是否为空，非进主页，空则登录
        if (SharedPrefManager.getInstance().isLogin()) {
//            startActivity(new Intent(FlashActivity.this, MainActivity.class));
            ContainerRuntime.INSTANCE.getRouter().open(this, "/main", null);
            UserInfoBean bean = SharedPrefManager.getInstance().getUserInfo();
            SnowGroundUtils.identify(bean.getName(), bean);
            SnowGroundUtils.trackStartUp(bean);
        } else {
            startActivity(new Intent(FlashActivity.this, LoginActivity.class));
            SnowGroundUtils.identify("", null);
            SnowGroundUtils.trackStartUp(null);
        }
        finish();
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return null;
    }

    @Override
    public void showNetError() {

    }
}
