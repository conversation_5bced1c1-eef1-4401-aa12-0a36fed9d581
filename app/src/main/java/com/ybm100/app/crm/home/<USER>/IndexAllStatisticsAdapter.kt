package com.ybm100.app.crm.home.ui

import android.content.Context
import android.content.ContextWrapper
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Paint.FontMetricsInt
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.RecyclerView
import com.ybm100.app.crm.R
import com.ybm100.app.crm.home.HomeViewModel
import com.ybm100.app.crm.home.bean.DataStatisticsResult
import com.ybm100.app.crm.home.bean.SalesStatistics
import com.ybm100.app.crm.home.bean.SalesStatisticsV2

class IndexAllStatisticsAdapter : RecyclerView.Adapter<IndexAllStatisticsAdapter.ViewHolder>() {

    private var list = listOf<DataStatisticsResult>()


    fun setData(data: List<DataStatisticsResult>?) {
        list = if (!data.isNullOrEmpty()) {
            data
        } else {
            listOf(DataStatisticsResult("--", "--", "--"),
                    DataStatisticsResult("--", "--", "--"),
                    DataStatisticsResult("--", "--", "--"),
                    DataStatisticsResult("--", "--", "--"),
                    DataStatisticsResult("--", "--", "--"),
                    DataStatisticsResult("--", "--", "--"))
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(p0: ViewGroup, p1: Int): IndexAllStatisticsAdapter.ViewHolder {
        return ViewHolder(p0, R.layout.item_home_module_index_statistics_all)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(viewHolder: IndexAllStatisticsAdapter.ViewHolder, position: Int) {
        viewHolder.bind(list[position])
    }


    inner class ViewHolder(parentView: ViewGroup, layoutId: Int)
        : RecyclerView.ViewHolder(LayoutInflater.from(parentView.context).inflate(layoutId, parentView, false)) {
        fun bind(salesStatistics: DataStatisticsResult) {
            itemView.findViewById<TextView>(R.id.tv_title).text = salesStatistics.index
            itemView.findViewById<TextView>(R.id.tv_content).text = salesStatistics.indexValue
            setGrowthRate(salesStatistics.rate ?: "", itemView.findViewById<TextView>(R.id.tv_rate))
        }

        /**
         * 设置增长率
         *
         * @param rate
         * @param textView
         */
        private fun setGrowthRate(rate: String, textView: TextView) {
            var rateBuilder = rate
            if (rateBuilder == "--" || rate == "0" || rate == "0.0" || rate == "0.00") {
                textView.visibility = View.INVISIBLE
            } else {
                textView.visibility = View.VISIBLE
            }
            if (TextUtils.isEmpty(rateBuilder)) {
                rateBuilder = "0.00"
            }
            var startText = "环比"
            getActivity(textView)?.let { activity ->
                val localHomeViewModule = ViewModelProviders.of(activity).get(HomeViewModel::class.java)
                if (localHomeViewModule.dateType == 1
                        || localHomeViewModule.dateType == 6) {
                    startText = "同比上周"
                }
            }

            var result: CharSequence = ""
            if (rateBuilder.startsWith("-")) {
                rateBuilder = rateBuilder.substring(1) // 负数去掉负号

                result = highlightEnd(textView.context, R.color.color_FF4741, R.drawable.home_arrow_reduce, startText, "1$rateBuilder%")
            } else { // 正数（包含0）
                result = highlightEnd(textView.context, R.color.color_00B377, R.drawable.home_arrow_increase, startText, "1$rateBuilder%")
            }
            textView.text = result
        }

        private fun getActivity(view: View): FragmentActivity? {
            var viewContext = view.context
            while (viewContext is ContextWrapper) {
                if (viewContext is FragmentActivity) {
                    return viewContext
                }
                viewContext = viewContext.baseContext
            }
            return null
        }

        fun highlightEnd(context: Context, highlightEndColor: Int, img: Int, startText: String? = "", endText: String? = ""): SpannableStringBuilder {
            val builder = SpannableStringBuilder()
            builder.append(startText)
            builder.append(endText)
            val highlightStart = startText?.length ?: 0
            val highlightEnd = highlightStart + (endText?.length ?: highlightStart)

            //尾部高亮
            builder.setSpan(ForegroundColorSpan(ContextCompat.getColor(context, highlightEndColor)), highlightStart, highlightEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
            builder.setSpan(VerticalImageSpan(context, img), highlightStart, highlightStart + 1, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE)

            return builder
        }


        inner class VerticalImageSpan(context: Context, resourceId: Int) : ImageSpan(context, resourceId) {

            override fun getSize(paint: Paint, text: CharSequence?, start: Int, end: Int,
                                 fontMetricsInt: FontMetricsInt?): Int {
                val drawable = drawable
                val rect = drawable.bounds
                if (fontMetricsInt != null) {
                    val fmPaint = paint.fontMetricsInt
                    val fontHeight = fmPaint.bottom - fmPaint.top
                    val drHeight = rect.bottom - rect.top
                    val top = drHeight / 2 - fontHeight / 4
                    val bottom = drHeight / 2 + fontHeight / 4
                    fontMetricsInt.ascent = -bottom
                    fontMetricsInt.top = -bottom
                    fontMetricsInt.bottom = top
                    fontMetricsInt.descent = top
                }
                return rect.right
            }

            override fun draw(canvas: Canvas, text: CharSequence?, start: Int, end: Int,
                              x: Float, top: Int, y: Int, bottom: Int, paint: Paint?) {
                val drawable = drawable
                canvas.save()
                var transY = 0
                transY = (bottom - top - drawable.bounds.bottom) / 2 + top
                canvas.translate(x, transY.toFloat())
                drawable.draw(canvas)
                canvas.restore()
            }
        }
    }


}
