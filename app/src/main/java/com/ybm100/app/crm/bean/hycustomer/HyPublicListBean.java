package com.ybm100.app.crm.bean.hycustomer;

import java.io.Serializable;
import java.util.List;

/**
 * 荷叶健康公海客户bean
 */
public class HyPublicListBean implements Serializable {
    private PageDataBean pageData;

    public PageDataBean getPageData() {
        return pageData;
    }

    public void setPageData(PageDataBean pageData) {
        this.pageData = pageData;
    }

    public static class PageDataBean {

        private List<HyPublicListBean.RowBean> rows;
        private boolean lastPage;

        public boolean isLastPage() {
            return lastPage;
        }

        public void setLastPage(boolean lastPage) {
            this.lastPage = lastPage;
        }

        public List<HyPublicListBean.RowBean> getRows() {
            return rows;
        }

        public void setRows(List<HyPublicListBean.RowBean> rows) {
            this.rows = rows;
        }
    }

    public static class RowBean implements Serializable {
        /**
         * address		string	地址
         * customerName		string	客户名称
         * distance		string	当前距离
         * id		number	@mock=$order(376,374)
         * receiveType		number	1、已被认领 2未被认领
         * sysUserId		number	@mock=$order(3049,3049)
         * sysUserName		string	@mock=$order('xt_zhongtai_24','xt_zhongtai_24')
         */
        private String address;
        private String ecAddress;
        private String customerName;
        private String ecCustomerName;
        private String distance;
        private String id;
        private String hyId;
        private String poiId;
        private String receiveType;
        private String sysUserId;
        private String sysUserName;
        private String registerFlag;
        private List<HyPublicListBean.RowBean.ServiceLines> serviceLines;//合作方icon

        public String getRegisterFlag() {
            return registerFlag;
        }

        public void setRegisterFlag(String registerFlag) {
            this.registerFlag = registerFlag;
        }

        public String getEcAddress() {
            return ecAddress;
        }

        public void setEcAddress(String ecAddress) {
            this.ecAddress = ecAddress;
        }

        public String getEcCustomerName() {
            return ecCustomerName;
        }

        public void setEcCustomerName(String ecCustomerName) {
            this.ecCustomerName = ecCustomerName;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getDistance() {
            return distance;
        }

        public void setDistance(String distance) {
            this.distance = distance;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getHyId() {
            return hyId;
        }

        public void setHyId(String hyId) {
            this.hyId = hyId;
        }

        public String getPoiId() {
            return poiId;
        }

        public void setPoiId(String poiId) {
            this.poiId = poiId;
        }

        public String getReceiveType() {
            return receiveType;
        }

        public void setReceiveType(String receiveType) {
            this.receiveType = receiveType;
        }

        public String getSysUserId() {
            return sysUserId;
        }

        public void setSysUserId(String sysUserId) {
            this.sysUserId = sysUserId;
        }

        public String getSysUserName() {
            return sysUserName;
        }

        public void setSysUserName(String sysUserName) {
            this.sysUserName = sysUserName;
        }

        public List<ServiceLines> getServiceLines() {
            return serviceLines;
        }

        public void setServiceLines(List<ServiceLines> serviceLines) {
            this.serviceLines = serviceLines;
        }

        public static class ServiceLines {
            private int code; // 1:B2B,2:灵芝问诊,3:SaaS
            private String name;// 名称
            private String lighten;//是否高亮
            private String androidIcon;//图标链接"http://xxx/xx.png"

            public int getCode() {
                return code;
            }

            public void setCode(int code) {
                this.code = code;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getLighten() {
                return lighten;
            }

            public void setLighten(String lighten) {
                this.lighten = lighten;
            }

            public String getIcon() {
                return androidIcon;
            }

            public void setIcon(String icon) {
                this.androidIcon = icon;
            }
        }
    }


}
