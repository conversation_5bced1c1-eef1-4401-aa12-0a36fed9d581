package com.ybm100.app.crm.task.bean

data class TaskGoodsListDataBean(
        val currentPage: Int? = 0, // 60434
        val lastPage: Boolean? = false, // false
        val other: Other? = Other(),
        val rows: List<Row?>? = listOf(),
        val total: Int? = 0 // 85283
) {
    data class Other(
            val totalProductNum: String? = "", // 34171
            val totalTaskNum: String? = "" // 25600
    )

    data class Row(
            val fob: String? = "", // 54833
            val grossMargin: String? = "", // 47543
            val imagesList: String? = "", // 测试内容kn1h
            val promoList: List<Promo?>? = listOf(),
            val showName: String? = "", // 测试内容ks61
            val spec: String? = "", // 测试内容1p3i
            val suggestPrice: String? = "", // 75303
            val taskFinishNum: String? = "", // 测试内容qx0f
            val taskName: String? = "", // 测试内容m3qc
            val taskProductNum: String? = "", // 70613
            val taskRate: Double? = 0.00, // 51167
            val taskTargetNum: String? = "", // 测试内容261t
            val taskTypeStr: String? = "", // 测试内容261t
            val isTeam: Boolean? = false,
            val taskId: String? = ""
    ) {
        data class Promo(
                val planDescription: String? = "", // 测试内容djym
                val promoType: String? = "", // 45411
                val promoTypeStr: String? = "", // 测试内容1466
                val uiType: Int? = 0 // 测试内容1466
        )
    }
}