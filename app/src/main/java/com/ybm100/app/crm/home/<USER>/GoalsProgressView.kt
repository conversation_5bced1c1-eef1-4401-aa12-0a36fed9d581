package com.ybm100.app.crm.home.module

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.LayerDrawable
import android.graphics.drawable.ScaleDrawable
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.xyy.canary.utils.DensityUtil
import com.xyy.common.util.ConvertUtils
import com.xyy.common.util.TimeUtils
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.utils.GlideLoadUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.home.HomeViewModel
import com.ybm100.app.crm.home.bean.GoalsProgressBean
import com.ybm100.app.crm.home.presenter.GoalsProgressPresenter
import com.ybm100.app.crm.utils.SnowGroundUtils
import com.ybm100.app.crm.utils.SpannableStringUtils
import com.ybm100.app.crm.utils.module.module.BaseModule
import com.ybm100.app.crm.widget.picker.PickerManager
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.layout_home_module_goals_progress.view.*
import java.util.*

class GoalsProgressView(context: Context) : BaseModule<GoalsProgressPresenter>(context), View.OnClickListener {
    private var mAdapter: GoalsProgressAdapter? = null
    private val mCalendar = Calendar.getInstance()
    private var mDisposable: Disposable? = null
    private var mAdapterListData: MutableList<GoalsProgressBean.Row?>? = mutableListOf()
    private var isExpanded = false

    override fun getContentLayoutId(): Int {
        return R.layout.layout_home_module_goals_progress
    }

    override fun onInit() {

        registerListener()

        initRecyclerView()

        initSvlEmptyView()
    }

    private fun initSvlEmptyView() {
        val emptyView = LayoutInflater.from(context).inflate(R.layout.platform_status_view_layout_empty, svl, false)

        emptyView.findViewById<LinearLayout>(R.id.status_view_btn_reload).apply {
            gravity = Gravity.CENTER_HORIZONTAL
        }

        emptyView.findViewById<ImageView>(R.id.status_view_iv_empty).apply {
            layoutParams = (layoutParams as LinearLayout.LayoutParams).apply {
                setMargins(0, ConvertUtils.dp2px(40f), 0, 0)
            }
            GlideLoadUtils.load(context, this, R.drawable.ic_empty_home_goals_progress)
        }

        emptyView.findViewById<TextView>(R.id.status_view_tv_empty).apply {
            text = "当前数据为空，请联系当地销运上传目标值"
            textSize = 12f
            setPadding(ConvertUtils.dp2px( 7f), 0, 0, ConvertUtils.dp2px( 20f))
        }

        svl?.setEmptyView(emptyView)
    }

    override fun onRefresh() {
        setDate()
    }

    override fun getPresenter(): Class<GoalsProgressPresenter>? {
        return GoalsProgressPresenter::class.java
    }

    fun onRequestGoalsProgressSuccess(response: RequestBaseBean<GoalsProgressBean?>?) {
        if (response?.data?.rows.isNullOrEmpty()) {
            svl?.showEmpty()
        } else {
            response?.data?.rows?.let {
//                mAdapterListData = it
                mAdapterListData?.clear()
                mAdapterListData?.addAll(it)
                isExpanded = false

                if (it.size > 1) {
                    tv_expand?.visibility = View.VISIBLE
                    tv_expand?.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_home_goals_expand, 0, 0, 0)
                    tv_expand?.text = "展开"

                    mAdapter?.setNewData(it.subList(0, 1))
                } else {
                    tv_expand.visibility = View.GONE

                    mAdapter?.setNewData(it)
                }

                svl?.showContent()
            }
        }

        refreshCallback?.refreshFinish(typeId, null)
    }

    override fun showNetError() {
        super.showNetError()
        refreshCallback?.refreshFinish(typeId, null)
        svl?.showEmpty()
    }

    private fun registerListener() {
        iv_question_mark?.setOnClickListener(this)
        tv_last_month?.setOnClickListener(this)
        tv_next_month?.setOnClickListener(this)
        tv_date?.setOnClickListener(this)
        tv_expand?.setOnClickListener(this)
    }

    private fun setDate(shouldTrack: Boolean = false) {
        tv_date?.text = TimeUtils.date2String(mCalendar.time, TimeUtils.DATE_FMT_YM)
        mPresenter?.mQueryMap?.set("years", TimeUtils.date2String(mCalendar.time, TimeUtils.DATE_FORMAT_YM))

        getActivity()?.let {
            val homeViewModel = ViewModelProvider(it).get(HomeViewModel::class.java)
            val key = if (homeViewModel.isGroup) {
                mPresenter?.mQueryMap?.remove("searchUserId")
                "groupId"
            } else {
                mPresenter?.mQueryMap?.remove("groupId")
                "searchUserId"
            }

            mPresenter?.mQueryMap?.set(key, homeViewModel.id)
        }

        requestGoalsProgress()

        if (shouldTrack) {
            SnowGroundUtils.track("mc-homepage-target", hashMapOf("timeInterval" to tv_date?.text?.toString()))
        }
    }

    private fun initRecyclerView() {
        recycler_view?.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            mAdapter = GoalsProgressAdapter()
            adapter = mAdapter
            isNestedScrollingEnabled = false
        }
    }

    private fun requestGoalsProgress() {
        svl?.showLoading(" ")
        mDisposable?.dispose()
        mDisposable = mPresenter?.requestGoalsProcess()
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_question_mark -> {
                val pop = PopupWindow(layoutInflater.inflate(R.layout.tooltip_text_home_module_goals_progress, null), ConvertUtils.dp2px( 150f), ViewGroup.LayoutParams.WRAP_CONTENT)
                pop.isOutsideTouchable = true
                pop.isFocusable = true

                if (Build.VERSION.SDK_INT >= 24) {
                    val p1 = IntArray(2)
                    v.getLocationInWindow(p1)

                    val p2 = IntArray(2)
                    v.getLocationInWindow(p2)

                    pop.showAtLocation(v, Gravity.NO_GRAVITY, p1[0] + ConvertUtils.dp2px( 11f) - ConvertUtils.dp2px( 75f), p2[1] + v.height)
                } else {
                    pop.showAsDropDown(v, -ConvertUtils.dp2px( 75f) + (v.width / 2), 0)
                }
            }
            R.id.tv_last_month -> {
                mCalendar.add(Calendar.MONTH, -1)
                setDate(true)

                UserBehaviorTrackingUtils.track("mc-homepage-target-lastmonth")
            }
            R.id.tv_next_month -> {
                mCalendar.add(Calendar.MONTH, 1)
                setDate(true)

                UserBehaviorTrackingUtils.track("mc-homepage-target-nextmonth")
            }
            R.id.tv_date -> {
                val startDate = Calendar.getInstance()
                startDate.set(1970, 1, 0)

                val endDate = Calendar.getInstance()
                endDate.set(2120, 11, 31)

                PickerManager.showDateSelectPicker(context, mCalendar, startDate, endDate, booleanArrayOf(true, true, false, false, false, false)) { date: Date, dataStr: String? ->
                    mCalendar.time = date
                    setDate(true)

                    UserBehaviorTrackingUtils.track("mc-homepage-target-month", mapOf("timeInterval" to (dataStr ?: "")))
                }
            }
            R.id.tv_expand -> {
                if (isExpanded) {
                    mAdapter?.data?.let {
                        if (it.size == 3){
                            mAdapter?.remove(2)
                        }
                        mAdapter?.remove(1)
                    }
                    tv_expand?.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_home_goals_expand, 0, 0, 0)
                    tv_expand?.text = "展开"

                    UserBehaviorTrackingUtils.track("mc-homepage-target-show", mapOf("target" to "hide"))
                } else {
                    mAdapterListData?.let {
                        mAdapter?.addData(it.subList(1, it.size))
                    }
                    tv_expand?.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_home_goals_collapse, 0, 0, 0)
                    tv_expand?.text = "收起"

                    UserBehaviorTrackingUtils.track("mc-homepage-target-show", mapOf("target" to "show"))
                }
                isExpanded = !isExpanded
            }
        }
    }

    class GoalsProgressAdapter : BaseQuickAdapter<GoalsProgressBean.Row, BaseViewHolder>(R.layout.item_home_module_goals_progress) {
        override fun convert(helper: BaseViewHolder?, item: GoalsProgressBean.Row?) {
            item?.run {
                try {
                    helper?.setText(R.id.tv_goal_name, title ?: "--")
                            ?.setText(R.id.tv_goal_progress_value, SpannableStringUtils.goalsProgress(mContext, "${completionValue ?: "--元"}", "/", "${targetValue ?: "--元"}"))
                            ?.setText(R.id.tv_progress_bar_value, "${"%.2f".format(proportion ?: 0)}%")
                            ?.setText(R.id.tv_goal_progress_name, describe ?: "--")

                    val progressBar = helper?.getView<ProgressBar>(R.id.progress_bar)

                    val bgDrawable = ColorDrawable(Color.parseColor("#FFFFFF"))
                    val secondaryProgressDrawable = ColorDrawable(Color.parseColor("#FFFFFF"))

                    val gradientDrawable = GradientDrawable()
                    gradientDrawable.cornerRadius = DensityUtil.dip2px(mContext, 2F).toFloat()
                    gradientDrawable.setColor(Color.parseColor(scheduleColour ?: "#00B377"))

                    val progressDrawable = ScaleDrawable(gradientDrawable, Gravity.START, 1F, -1F)


                    val layerDrawable = LayerDrawable(arrayOf(bgDrawable, secondaryProgressDrawable, progressDrawable))
                    layerDrawable.setId(0, android.R.id.background)
                    layerDrawable.setId(1, android.R.id.secondaryProgress)
                    layerDrawable.setId(2, android.R.id.progress)

                    progressBar?.progressDrawable = layerDrawable
                    progressBar?.progress = proportion?.toInt() ?: 0

                    helper?.setBackgroundColor(R.id.cl, Color.parseColor(backgroundColour
                            ?: "#EEFDF2"))
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

}