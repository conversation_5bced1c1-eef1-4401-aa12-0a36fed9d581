package com.ybm100.app.crm.ui.fragment.drugstore.minedrug;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xyy.common.widget.GridSpacingItemDecoration;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.drugstore.PublicCustomerDetailBean;
import com.ybm100.app.crm.constant.DrugstoreConstants;
import com.ybm100.app.crm.contract.drugstore.PublicCustomerDetailContract;
import com.ybm100.app.crm.order.adapter.PublicDetailPicAdapter;
import com.ybm100.app.crm.presenter.drugstore.YBMBaseInfoPublicCustomerDetailPresenter;
import com.ybm100.app.crm.ui.adapter.drugstore.ContactBaseInfoAdapter;
import com.ybm100.app.crm.utils.FormatUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:04
 * 基本信息
 */
public class YBMDrugstoreBaseInfoUnRegisterFragment extends BaseMVPCompatFragment<YBMBaseInfoPublicCustomerDetailPresenter> implements PublicCustomerDetailContract.IBaseInfoPublicCustomerDetailView {
    @BindView(R.id.tv_customer_name)
    TextView tvCustomerName;
    @BindView(R.id.tv_customer_type)
    TextView tvCustomerType;
    @BindView(R.id.tv_detail_address)
    TextView tvDetailAddress;
    @BindView(R.id.tv_business_time)
    TextView tvBusinessTime;
    @BindView(R.id.tv_phone)
    TextView tvPhone;
    @BindView(R.id.tv_mobile_phone)
    TextView tvMobilePhone;
    @BindView(R.id.tv_remark)
    TextView tvRemark;
    @BindView(R.id.recycle_view_photo)
    RecyclerView recycleViewPhoto;
    @BindView(R.id.layout_claim)
    LinearLayout layoutClaim;
    @BindView(R.id.tv_bd)
    TextView tvBd;
    @BindView(R.id.bd_group)
    Group bdGroup;
    @BindView(R.id.tv_poi_id)
    TextView tvPoiId;
    @BindView(R.id.tv_gjr)
    TextView tvGJR;
    @BindView(R.id.tv_poi_register_time)
    TextView tvPoiRegisterTime;


    //联系人
    private boolean expand;
    private ContactBaseInfoAdapter mAdapter;
    private List<ContactBean> topList;
    private List<ContactBean> allList;
    private ConstraintLayout layout;
    //客户id
    private String id;
    //poiId
    private String poiId;
    private String customerName;

    public static YBMDrugstoreBaseInfoUnRegisterFragment newInstance(String merchantId) {
        Bundle args = new Bundle();
        YBMDrugstoreBaseInfoUnRegisterFragment fragment = new YBMDrugstoreBaseInfoUnRegisterFragment();
        args.putString(DrugstoreConstants.INTENT_KEY_OPENSEAID, merchantId);
        fragment.setArguments(args);
        return fragment;
    }

    public static YBMDrugstoreBaseInfoUnRegisterFragment newInstance(String merchantId, String poiId) {
        Bundle args = new Bundle();
        YBMDrugstoreBaseInfoUnRegisterFragment fragment = new YBMDrugstoreBaseInfoUnRegisterFragment();
        args.putString(DrugstoreConstants.INTENT_KEY_OPENSEAID, merchantId);
        args.putString(DrugstoreConstants.INTENT_KEY_POI_ID, poiId);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_ybm_private_detail_base_info;
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return YBMBaseInfoPublicCustomerDetailPresenter.newInstance();
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        if (getArguments() != null) {
            id = getArguments().getString(DrugstoreConstants.INTENT_KEY_OPENSEAID);
            poiId = getArguments().getString(DrugstoreConstants.INTENT_KEY_POI_ID);
//            isFromHY = getArguments().getBoolean(DrugstoreConstants.INTENT_ACTION_FROM_HY,false);
        }
        layoutClaim.setVisibility(View.GONE);
    }

    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        super.onLazyInitView(savedInstanceState);
        mPresenter.searchOpenSeaDetail(id);
    }

    @Override
    public void searchOpenSeaDetailSuccess(RequestBaseBean<PublicCustomerDetailBean> baseBean) {
        if (baseBean == null || baseBean.getData() == null) return;
        PublicCustomerDetailBean detailBean = baseBean.getData();
        customerName = detailBean.getCustomerName();
        if (2 == detailBean.getRegisterFlag()) {
            //详细地址
            tvDetailAddress.setText(FormatUtils.textFormat(detailBean.getAddress()));
        } else {
            //详细地址
            tvDetailAddress.setText(FormatUtils.textFormat(detailBean.getEcAddress()));
        }
        //门店名称(poi地址)
        tvCustomerName.setText(FormatUtils.textFormat(TextUtils.isEmpty(detailBean.getEcCustomerName()) ? detailBean.getCustomerName() : detailBean.getEcCustomerName()));
        //门店品类
        tvCustomerType.setText(FormatUtils.textFormat(detailBean.getPoiCategory()));
        //营业时间
        tvBusinessTime.setText(FormatUtils.textFormat(detailBean.getBusinessHours()));
        //座机电话
        tvPhone.setText(FormatUtils.textFormat(detailBean.getPoiPhone()));
        //手机号码
        tvMobilePhone.setText(FormatUtils.textFormat(detailBean.getPoiContactMobile()));
        //备注说明
        tvRemark.setText(FormatUtils.textFormat(detailBean.getPoiRemark()));
        // 门店ID
        tvPoiId.setText(FormatUtils.textFormat(detailBean.getPoiId()));
        // 工商注册时间
        tvPoiRegisterTime.setText(FormatUtils.textFormat(detailBean.getPoiRegisterTime()));
        //BD
        StringBuilder text = new StringBuilder();
        for (int i = 0; detailBean.getBdInfos() != null && i < detailBean.getBdInfos().size(); i++) {
            String bd_name = detailBean.getBdInfos().get(i).getRealName();
            text.append(bd_name);
            if (i < detailBean.getBdInfos().size() - 1) {
                text.append("、");
            }
        }
        tvBd.setText(FormatUtils.textFormat(text.toString()));
        //跟进人
        StringBuilder textFollow = new StringBuilder();
        for (int i = 0; detailBean.getFollowUserInfos() != null && i < detailBean.getFollowUserInfos().size(); i++) {
            String follow_name = detailBean.getFollowUserInfos().get(i).getRealName();
            textFollow.append(follow_name);
            if (i < detailBean.getFollowUserInfos().size() - 1) {
                textFollow.append("、");
            }
        }
        tvGJR.setText(FormatUtils.textFormat(textFollow.toString()));
        //门头照
        GridLayoutManager linearLayoutManager = new GridLayoutManager(mContext, 3);
        GridSpacingItemDecoration itemDecoration = new GridSpacingItemDecoration(3, getResources().getDimensionPixelOffset(R.dimen.dp10), getResources().getDimensionPixelOffset(R.dimen.dp10), true);
        recycleViewPhoto.addItemDecoration(itemDecoration);
        recycleViewPhoto.setLayoutManager(linearLayoutManager);
        if (!TextUtils.isEmpty(detailBean.getPoiImageUrl())) {
            String[] imageStr = detailBean.getPoiImageUrl().split(",");
            List<String> imageInfo = new ArrayList<>(Arrays.asList(imageStr));
            PublicDetailPicAdapter adapter = new PublicDetailPicAdapter(R.layout.item_image_door_photo, imageInfo, false, linearLayoutManager);
            recycleViewPhoto.setAdapter(adapter);
        }
        layoutClaim.setVisibility(View.GONE);
        bdGroup.setVisibility(View.VISIBLE);
    }

    @Override
    public void receiveSuccess(RequestBaseBean baseBean, String merchantId) {
    }

    @Override
    public void getContactListSuccess(RequestBaseBean<List<ContactBean>> baseBean) {

    }


    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        RxBus.get().register(this);
    }

    @Override
    public void onDetach() {
        super.onDetach();
        RxBus.get().unRegister(this);
    }

    @Override
    public void showNetError() {

    }
}
