import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/check_license_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/popover/common_popover.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_add_plan_single_time_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/customer_sku_collect_dialog.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/plan/visit_plan_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class YBMCustomerPrivateItem extends StatelessWidget {
  final YBMCustomerPrivateItemModel model;
  final bool hasLocation;
  final String serviceInterface;
  final String licenseCode;
  final String? locationLat;
  final String? locationLong;

  YBMCustomerPrivateItem(
    this.model, {
    this.hasLocation = true,
    this.serviceInterface = '',
    this.licenseCode = "",
    this.locationLat,
    this.locationLong,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFEFEFF4),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(7),
        ),
        child: Column(
          children: [
            this.merchantNameWidget(),
            this.merchantAddressWidget(),
            this.tagWidget(),
            this.getPurchaseInfoWidget(context),
            this.getControlGoodsWidget(context),
            this.getActionWidget(context),
          ],
        ),
      ),
    );
  }

  // 客户名称及状态
  Widget merchantNameWidget() {
    Color statusColor = Color(0xFF35C561);
    if (model.merchantStatusName == "已冻结") {
      statusColor = Color(0xFFFFC000);
    } else if (model.merchantStatusName == "未注册" ||
        model.merchantStatusName == "沉默" ||
        model.merchantStatusName == "流失") {
      statusColor = Color(0xFFFE3D3D);
    }
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 10),
      child: Row(
        children: [
          Expanded(
            child: Text(
              this.model.getName(),
              style: TextStyle(
                color: Color(0xFF333333),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 10),
          Text(
            "${model.merchantStatusName}",
            style: TextStyle(color: statusColor, fontSize: 14),
          )
        ],
      ),
    );
  }

  // 距离及位置
  Widget merchantAddressWidget() {
    String locationStr = this.hasLocation ? "距您${model.distance}" : "获取不到当前位置";
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 9),
      child: Row(
        children: [
          Text(
            locationStr,
            style: TextStyle(color: Color(0xFF666666), fontSize: 12),
          ),
          SizedBox(width: 5),
          Expanded(
            child: Text(
              model.getAddress(),
              style: TextStyle(color: Color(0xFF666666), fontSize: 12),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 标签行
  Widget tagWidget() {
    List<Widget> tags = [];
    // 资质状态
    if (this.model.licenseStatusName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.licenseStatusName}", Color(0xFFECA100)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 药店类型
    if (this.model.customerTypeName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.customerTypeName}", Color(0xFF35C561)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 药店级别
    if (this.model.biLevelName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.biLevelName}" + "级别", Color(0xFF35C561)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 生命周期
    if (this.model.biLifecycleName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.biLifecycleName}", Color(0xFF35C561)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 审核状态
    if (this.model.statusName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.statusName}", Color(0xFF35C561)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 是否新开业
    if ("${this.model.poiRegisterFlag}" == "1") {
      tags.add(
        getTagItem("新开业", Color(0xFFFF2121)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 距上次拜访时间
    tags.add(Spacer());
    tags.add(getVisitTimeWidget());

    return Container(
      padding: EdgeInsets.only(left: 10, top: 10),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: tags,
      ),
    );
  }

  // 拜访时间
  Widget getVisitTimeWidget() {
    return Container(
      height: 18,
      decoration: BoxDecoration(
        color: Color(0xFFFAFBFA),
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(9),
        ),
      ),
      padding: EdgeInsets.only(left: 6.5, right: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/customer/customer_private_visit_icon.png',
            width: 12,
          ),
          SizedBox(width: 4),
          Text(
            this.hasVisitTime ? '距上次拜访' : '未拜访',
            style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
          ),
          Visibility(
            visible: this.hasVisitTime,
            child: Text(
              '${this.model.overLastVisitDays}',
              style: TextStyle(color: Color(0xFF333333), fontSize: 12),
            ),
          ),
          Visibility(
            visible: this.hasVisitTime,
            child: Text(
              '天',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
            ),
          )
        ],
      ),
    );
  }

  // 标签
  Widget getTagItem(String tag, Color color) {
    return Container(
      padding: EdgeInsets.fromLTRB(3.5, 0.5, 3.5, 0.5),
      decoration: BoxDecoration(
        border: Border.all(color: color, width: 0.5),
      ),
      child: Text(
        tag,
        style: TextStyle(color: color, fontSize: 10),
      ),
    );
  }

  // 合作状态
  Widget getCooperationWidget() {
    // ImageCatchWidget
    List<String> imagePaths = this
            .model
            .serviceLines
            ?.map((e) => this.serviceInterface + e.iOSIcon)
            .toList() ??
        [];
    List<Widget> items = [];
    imagePaths.forEach((e) {
      items.add(ImageCatchWidget(
        url: e,
        h: 14,
        fit: BoxFit.fill,
      ));
      items.add(SizedBox(width: 5));
    });
    items.add(Spacer());
    items.add(Container(
      height: 18,
      decoration: BoxDecoration(
        color: Color(0xFFFAFBFA),
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(9),
        ),
      ),
      padding: EdgeInsets.only(left: 6.5, right: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/customer/customer_private_visit_icon.png',
            width: 12,
          ),
          SizedBox(width: 4),
          Text(
            this.hasVisitTime ? '距上次拜访' : '未拜访',
            style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
          ),
          Visibility(
            visible: this.hasVisitTime,
            child: Text(
              '${this.model.overLastVisitDays}',
              style: TextStyle(color: Color(0xFF333333), fontSize: 12),
            ),
          ),
          Visibility(
            visible: this.hasVisitTime,
            child: Text(
              '天',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
            ),
          )
        ],
      ),
    ));
    return Container(
      padding: EdgeInsets.only(left: 10, top: 10),
      child: Row(
        children: items,
      ),
    );
  }

  /// 是否拜访过
  bool get hasVisitTime {
    return this.model.overLastVisitDays != "-";
  }

  /// 采购信息
  Widget getPurchaseInfoWidget(BuildContext context) {
    return Visibility(
      visible: this.isRegister,
      child: Container(
        padding: EdgeInsets.only(top: 10),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Container(
              color: Color(0xFFFAFAFA),
              padding: EdgeInsets.only(top: 5, bottom: 5, left: 10, right: 10),
              child: Wrap(
                children: [
                  getPurchaseItem(
                      context, "本月采购额", "${model.thisMonthOrderAmt}"),
                  getPurchaseItem(
                      context, "本月采购订单数", "${model.thisMonthOrderNum}单"),
                  Visibility(
                    visible: this.hasLastMonthData,
                    child: getPurchaseItem(
                        context, "上月采购额", "${model.lastMonthOrderAmt}"),
                  ),
                  Visibility(
                    visible: this.hasLastMonthData,
                    child: getPurchaseItem(
                        context, "上月采购订单数", "${model.lastMonthOrderNum}单"),
                  ),
                ],
              ),
            ),
            Positioned(
              right: 4,
              child: Visibility(
                visible: model.getLicenseValidateStatus("") !=
                    LICENSE_VALIDATE_STATUS_COMPOSE_NORMAL,
                child: Image.asset(
                  this.licenceStatusImage,
                  width: 43,
                  height: 43,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget getPurchaseItem(BuildContext context, String title, String content) {
    return Container(
      width: (MediaQuery.of(context).size.width - 40) / 2,
      height: 25,
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
            text: title,
            style: TextStyle(
              color: Color(0xFF8E8E93),
              fontSize: 12,
            ),
            children: [
              TextSpan(
                text: content,
                style: TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 12,
                ),
              ),
            ]),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// 是否注册
  bool get isRegister {
    return "${this.model.registerFlag}" == "1";
  }

  bool get isShowLicence {
    return model.licenseValidateFlag == 1 || model.licenseValidateFlag == 2;
  }

  String get licenceStatusImage {
    var licenseCodeI = model.getLicenseValidateStatus(this.licenseCode);
    print(licenseCodeI);
    switch (licenseCodeI) {
      //必填临期
      case LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_ADVENT:
        return "assets/images/customer/customer_required_advent.png";
      //非必临期
      case LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_ADVENT:
        return "assets/images/customer/customer_no_required_advent.png";
      //必填过期
      case LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_EXPIRE:
        return "assets/images/customer/customer_required_expire.png";
      //非必过期
      case LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_EXPIRE:
        return "assets/images/customer/customer_no_required_expire.png";
    }
    return "";
    if (model.licenseValidateFlag == 1) {
      return "assets/images/customer/customer_licence_near_past.png";
    }
    if (model.licenseValidateFlag == 2) {
      return "assets/images/customer/customer_licence_pasted.png";
    }
    return "assets/images/customer/customer_licence_near_past.png";
  }

  // String transFromLicenseValidateStatus() {
  //   if(this.licenseCode.contains("12")){
  //     return "assets/images/customer/customer_required_expire.png";
  //   }
  //   if(this.licenseCode.contains("22")){
  //     return "assets/images/customer/customer_no_required_expire.png";
  //   }
  //   if(this.licenseCode.contains("11")){
  //     return "assets/images/customer/customer_required_advent.png";
  //   }
  //   if(this.licenseCode.contains("21")){
  //     return "assets/images/customer/customer_no_required_advent.png";
  //   }
  //   return "";
  // }

  bool get hasLastMonthData {
    return "${model.lastMonthOrderAmt}".isEmpty == false &&
        "${model.lastMonthOrderNum}".isEmpty == false;
  }

  /// 控销商品集入口
  Widget getControlGoodsWidget(BuildContext context) {
    List<Widget> list = [];

    list.addAll(this
            .model
            .bindSkuCollect
            ?.map(
              (e) => GestureDetector(
                onTap: () {},
                behavior: HitTestBehavior.opaque,
                child: getControlItem(context, e),
              ),
            )
            .toList() ??
        []);
    list.add(Divider(color: Color(0xFFF6F6F6), height: 0.5, thickness: 0.5));
    return Container(
      child: Column(
        children: list,
      ),
    );
  }

  Widget getControlItem(
      BuildContext context, CustomerSkuCollectData controlModel) {
    GlobalKey _authKey = GlobalKey();
    return GestureDetector(
      onTap: () {
        this.jumpControlGoods(context, controlModel);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.all(10),
        child: Row(
          children: [
            Text(
              "${controlModel.skuCollectName}",
              style: TextStyle(
                color: Color(0xFF333333),
                fontSize: 14,
              ),
            ),
            SizedBox(width: 5),
            Text(
              "(还有${controlModel.bindCountdown}天掉落)",
              style: TextStyle(
                color: Color(0xFF8E8E93),
                fontSize: 12,
              ),
            ),
            TextButton(
                onPressed: () {
                  showPopoverTips(context, _authKey, controlModel.tips ?? "--");
                },
                key: _authKey,
                child: Image.asset(
                  'assets/images/funnel/funnel_tips_icon.png',
                  width: 12,
                  height: 12,
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding: MaterialStateProperty.all<EdgeInsets>(
                      EdgeInsets.only(right: 10, left: 10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                )),
            Spacer(),
            Image.asset(
              'assets/images/base/icon_arrow_right.png',
              width: 12,
              height: 12,
            ),
          ],
        ),
      ),
    );
  }

  /// 商品集掉落规则弹窗
  void showPopoverTips(
      BuildContext context, GlobalKey anchorKey, String content) {
    showTipsPopover(
      context: context,
      anchorKey: anchorKey,
      content: Text(
        content,
        style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
      ),
    );
  }

  ValueNotifier<bool> notifier = new ValueNotifier(true);
  ValueNotifier<List> addVisitPlanBtnnotifier =  ValueNotifier([]);

  /// 检查已添加计划列表请求
  checkedAddVisitPlanBtn() async {
   var relust =  await NetworkV2<VisitPlanDataModel>(VisitPlanDataModel()).requestDataV2(
      'visit/plan/list',
      parameters: {'lat': locationLat, 'lng': locationLong},
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
      showErrorToast:false,
    );
    addVisitPlanBtnnotifier.value = relust.getData()?.planList ?? [];
  }

  /// 底部按钮
  Widget getActionWidget(BuildContext context) {
    checkedAddVisitPlanBtn();
    // 请求计划列表
    return ValueListenableBuilder(
        valueListenable: addVisitPlanBtnnotifier,
        builder: (context, List planList, child) {
    // 判断是否已加入计划
    if(planList.isNotEmpty) {
    notifier.value = !planList.any((e) => e.customerId == model.id);
    }else {
      notifier.value = true;
    }
      return Container(
      height: 50,
      child: Row(
        children: [
          Visibility(
            visible: "${model.cartNum}" != "0",
            child: GestureDetector(
              onTap: this.jumpToShoppingCar,
              behavior: HitTestBehavior.opaque,
              child: this.shopCarWidget(),
            ),
          ),
          Spacer(),
          Visibility(
            visible: "${model.distributable}" != "0",
            child: this.buttonFor('分配', () {
              this.allocCollectAction(context);
            }),
          ),
          this.buttonFor('打电话', () {
            this.callPhoneAction(context);
          }),
          this.buttonFor('加计划', () {
            joinPlan();
          }, notifier: notifier),
          this.buttonFor('打拜访', () {
            CheckLicenseDialog().showCheckLicenseDialog(
                context,
                model.licenseValidateMust,
                model.licenseValidateIssue,
                model.merchantStatusName == "已冻结",
                continueVisitCallback: jumpToAddVisit);
          }),
        ],
      ),
    );
    });
  }

  Widget buttonFor(String title, VoidCallback onPressed,
      {ValueNotifier<bool>? notifier}) {
    return ValueListenableBuilder(
        valueListenable: notifier ?? ValueNotifier(true),
        builder: (context, bool value, child) {
          return TextButton(
              onPressed: value ? onPressed : () {},
              child: Container(
                height: 30,
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xFF8E8E93), width: 0.5),
                  borderRadius: BorderRadius.circular(15),
                  color: Color(value ? 0xFFFFFFFF : 0xFFF4F4F4),
                ),
                padding: EdgeInsets.only(left: 10, right: 10),
                child: Center(
                  child: Text(
                    title,
                    style: TextStyle(
                        color: Color(value ? 0xFF333333 : 0xFF96969A),
                        fontSize: 14),
                  ),
                ),
              ),
              style: ButtonStyle(
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding: MaterialStateProperty.all<EdgeInsets>(
                    EdgeInsets.only(right: 10)),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ));
        });
  }

  Widget shopCarWidget() {
    return Container(
      padding: EdgeInsets.only(left: 24),
      constraints: BoxConstraints(minWidth: 40),
      child: Stack(
        // alignment: Alignment.centerRight,
        children: [
          Container(
            padding: EdgeInsets.only(right: 5, top: 5),
            child: Image.asset(
              'assets/images/customer/customer_car_icon.png',
              width: 21,
              height: 21,
            ),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              height: 12,
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(6),
              ),
              constraints: BoxConstraints(minWidth: 12),
              child: Center(
                child: Text(
                  '${model.cartNum}',
                  style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 9),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 跳转
  /// 跳转购物车
  void jumpToShoppingCar() {
    String router =
        "/goods_recommend_page?merchantId=${model.merchantId}&customerId=${model.id}&selectedIndex=0";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

//加计划
  void joinPlan() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<CustomerAddPlanSingleTimeData>(
            CustomerAddPlanSingleTimeData())
        .requestDataV2(
      'visit/plan/add',
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
      parameters: {
        'customerId': model.id,
      },
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      notifier.value = false;
    }
  }

  /// 跳转添加拜访
  void jumpToAddVisit() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<ScheduleExternalModel>(ScheduleExternalModel())
        .requestDataV2(
      'task/v290/toAddVisit',
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
      parameters: {
        'customerId': model.id,
        'customerType': 1,
      },
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      bool isBDM = await UserInfoUtil.isBDMOrGJRBDM();
      String roleJSON = await UserAuthManager.getRoleJSONString();
      String externalJson = jsonEncode(result.getData()?.toJson() ?? {});
      String roleStr = Uri.encodeComponent(roleJSON);
      String externalStr = Uri.encodeComponent(externalJson);
      // BDM、跟进人BDM跳转陪访   BD、跟进人跳添加拜访
      if (isBDM) {
        var router =
            '/add_accompany_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      } else {
        var router =
            '/add_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      }
    }
  }

  /// 分配
  void allocCollectAction(BuildContext context) async {
    var sysUserId = await UserInfoUtil.sysUserId();
    // 重置商品集选中信息
    model.bindSkuCollect?.forEach((element) {
      element.isCheck = false;
    });
    List<CustomerSkuCollectData> list = model.bindSkuCollect
            ?.where((element) => element.oaUserId?.toString() == sysUserId)
            .toList() ??
        [];
    if (list.length > 1) {
      List<CustomerSkuCollectData>? result =
          await CustomerSkuCollectDialog.showSkuCollectDialog(
        context,
        list,
        title: "选择要分配的商品集",
        rightText: "下一步",
      );
      if (result != null && result.length > 0) {
        this.jumpToSelectPeople(result);
      }
    } else if (list.length == 1) {
      this.jumpToSelectPeople(list);
    } else {
      XYYContainer.toastChannel.toast("暂无可分配商品集");
    }
  }

  /// 跳转选择分配人员
  void jumpToSelectPeople(List<CustomerSkuCollectData> result) {
    String routerPath =
        "xyy://crm-app.ybm100.com/alloc_people?type=1&isAccompanySelect=1";
    XYYContainer.open(routerPath, callback: (value) {
      if (value != null) {
        String? popleId = value["selectdId"];
        String skuCollectCodes = result.map((e) => e.skuCollectCode).join(',');
        if (popleId != null && skuCollectCodes.length > 0) {
          this.requestAlloc(popleId, skuCollectCodes);
        }
      }
    });
  }

  /// 请求分配
  void requestAlloc(String bindUserId, String skuCollectCodes) async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'customer/private/bind/user',
      parameters: {
        'bindUserId': bindUserId,
        'skuCollectCodes': skuCollectCodes,
        'customerId': model.id,
      },
      method: RequestMethod.POST,
      contentType: RequestContentType.FORM,
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      XYYContainer.toastChannel.toast("分配成功");
    }
  }

  /// 拨打电话
  void callPhoneAction(BuildContext context) async {
    if (model.contactList?.length == 0) {
      XYYContainer.toastChannel.toast('联系人为空，请添加联系人');
      return;
    }
    YBMCustomerCallPhoneWidget.showContactListView(
      context: context,
      contactList: model.contactList ?? [],
      customerId: "${model.id ?? ""}",
      customerName: this.model.getName(),
    );
  }

  /// 跳转商品集
  void jumpControlGoods(
      BuildContext context, CustomerSkuCollectData controlModel) async {
    if ('${controlModel.skuCollectType}' == "1") {
      /// 普药商品集跳转 商品管理
      String router =
          "/goods_recommend_page?merchantId=${model.merchantId}&customerId=${model.id}&selectedIndex=4";
      router = Uri.encodeFull(router);
      XYYContainer.open(router);
    } else {
      /// 其他控销商品集跳转 控销管理页面
      var router =
          "/control_manager_page?merchantId=${model.merchantId}&customerId=${model.id}";
      router = Uri.encodeFull(router);
      XYYContainer.open(router);
    }
  }
}
